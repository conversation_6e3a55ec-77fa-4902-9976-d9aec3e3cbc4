#!/usr/bin/env python3
"""
实验控制器模块
负责协调各个模块，实现完整的集群实验流程
"""

import rospy
from .config import SwarmConfig
from ..detection.multi_target_detector import MultiTargetDetector
from ..analysis.flash_analyzer import FlashAnalyzer
from ..analysis.spatial_analyzer import SpatialAnalyzer
from ..behavior.swarm_behavior import SwarmBehavior
from ..utils.ros_utils import ROSUtils

class ExperimentController:
    """实验控制器"""
    
    def __init__(self):
        rospy.init_node('simple_2d_swarm_experiment', anonymous=True)
        
        # 初始化配置
        self.config = SwarmConfig()
        
        # 初始化ROS工具
        self.ros_utils = ROSUtils(self.config)
        publishers = self.ros_utils.get_publishers()
        
        # 初始化各个模块
        self.detector = MultiTargetDetector(self.config)
        self.flash_analyzer = FlashAnalyzer(self.config)
        self.spatial_analyzer = SpatialAnalyzer(self.config)
        self.behavior_executor = SwarmBehavior(self.config, publishers['velocity'])
        
        # 实验状态
        self.detected_targets_2d = []
        self.track_targets_2d = {}
        
        rospy.loginfo(f"🚀 实验控制器初始化完成 - 节点: {self.config.node_name}")
        
    def start_2d_detection(self):
        """启动2D空间检测"""
        # 清空之前的检测结果
        self.detected_targets_2d = []
        self.track_targets_2d = {}
        
        # 启动多摄像头检测
        all_camera_detections = self.detector.start_2d_detection()
        
        # 分析2D空间信息
        self.analyze_2d_spatial_info_multi_target(all_camera_detections)
    
    def analyze_2d_spatial_info_multi_target(self, all_camera_detections):
        """分析2D空间信息 - 批量优化版本"""
        rospy.loginfo("🧠 分析2D空间信息（批量优化版本）...")

        # 检查是否有缓存的检测结果
        if not all_camera_detections:
            rospy.logwarn("❌ 未找到检测结果缓存")
            return

        # 1. 先对所有检测到的目标进行空间计算并输出
        self.spatial_analyzer.analyze_spatial_info(all_camera_detections)

        # 2. 进行灯语分析，找出有效指令的目标
        valid_targets = self.flash_analyzer.analyze_flash_patterns(all_camera_detections)
        
        if not valid_targets:
            return

        # 3. 只对有效指令的目标进行空间计算
        candidates = self.spatial_analyzer.calculate_target_spatial_info(all_camera_detections, valid_targets)
        
        if not candidates:
            return

        # 4. 选择最优目标并执行
        best_target = self.spatial_analyzer.select_best_candidate(candidates)
        
        if best_target:
            # 执行最优目标的行为
            self.behavior_executor.execute_behavior(best_target)
    
    def show_2d_params(self):
        """显示2D参数"""
        print("\n⚙️ 2D空间参数:")
        for key, value in self.config.spatial_params.items():
            print(f"  {key}: {value}")

        print("\n📹 摄像头朝向配置:")
        for cam, angle in self.config.camera_orientations.items():
            direction = self.config.get_direction_description(angle)
            print(f"  {cam}: {angle}° ({direction})")
            
        print("\n🔧 检测配置:")
        print(f"  detection_duration: {self.config.config['detection_duration']}s")
        print(f"  real_width: {self.config.config['real_width']}m")
        
        print("\n⏹️ 智能早停配置:")
        print(f"  early_stop_enabled: {self.config.config.get('early_stop_enabled', False)}")
        if self.config.config.get('early_stop_enabled', False):
            print(f"  early_stop_no_target_duration: {self.config.config.get('early_stop_no_target_duration', 2.0)}s")
            print(f"  min_detection_duration: {self.config.config.get('min_detection_duration', 1.0)}s")
        else:
            print("  (智能早停已禁用)")
            
        print("\n🔆 灯语分析配置:")
        print(f"  flash_num_frames: {self.config.config.get('flash_num_frames', 36)}帧")
        print(f"  flash_start_frame: {self.config.config.get('flash_start_frame', 7)}")
        print(f"  flash_threshold: {self.config.config.get('flash_threshold', 0.2)}")

    def adjust_2d_params(self):
        """调整2D参数"""
        print("\n🔧 调整2D参数")
        print("="*50)
        print("1. 空间计算参数")
        print("2. 检测配置参数")
        print("3. 智能早停参数")
        print("="*50)
        
        choice = input("选择参数类型 (1-3): ").strip()
        
        if choice == '1':
            self.show_2d_params()
            param = input("输入要调整的参数名: ").strip()
            if param in self.config.spatial_params:
                try:
                    value = float(input(f"输入新值 (当前: {self.config.spatial_params[param]}): "))
                    self.config.update_spatial_param(param, value)
                    print(f"✅ {param} 已更新为 {value}")
                except ValueError:
                    print("❌ 无效数值")
        
        elif choice == '2':
            print(f"\n📹 检测配置参数:")
            print(f"  detection_duration: {self.config.config['detection_duration']}s")
            print(f"  real_width: {self.config.config['real_width']}m")
            
            param = input("输入要调整的参数名 (detection_duration/real_width): ").strip()
            if param in ['detection_duration', 'real_width']:
                try:
                    value = float(input(f"输入新值 (当前: {self.config.config[param]}): "))
                    self.config.update_config(param, value)
                    print(f"✅ {param} 已更新为 {value}")
                except ValueError:
                    print("❌ 无效数值")
        
        elif choice == '3':
            print(f"\n⏹️ 智能早停参数:")
            print(f"  early_stop_enabled: {self.config.config.get('early_stop_enabled', False)}")
            print(f"  early_stop_no_target_duration: {self.config.config.get('early_stop_no_target_duration', 2.0)}s")
            print(f"  min_detection_duration: {self.config.config.get('min_detection_duration', 1.0)}s")
            
            param = input("输入要调整的参数名: ").strip()
            if param == 'early_stop_enabled':
                value = input(f"启用智能早停? (y/n, 当前: {self.config.config.get(param, False)}): ").strip().lower()
                self.config.update_config(param, value in ['y', 'yes', 'true', '1'])
                print(f"✅ {param} 已更新为 {self.config.config[param]}")
            elif param in ['early_stop_no_target_duration', 'min_detection_duration']:
                try:
                    value = float(input(f"输入新值 (当前: {self.config.config.get(param, 0)}): "))
                    self.config.update_config(param, value)
                    print(f"✅ {param} 已更新为 {value}")
                except ValueError:
                    print("❌ 无效数值")
            else:
                print("❌ 无效参数名")
        else:
            print("❌ 无效选择")

    def show_detected_targets_2d(self):
        """显示2D检测目标"""
        detection_results = self.detector.get_detection_results()
        
        print(f"\n🎯 2D检测结果:")
        if detection_results:
            for cam_name, detections in detection_results.items():
                total_detections = sum(len(dets) for _, dets in detections)
                print(f"  {cam_name}: {len(detections)}帧, {total_detections}个检测")
        else:
            print("  暂无检测结果")

    def run_experiment(self):
        """运行实验（精简菜单）"""
        while not rospy.is_shutdown():
            print("\n" + "="*60)
            print("🚗 简化2D空间感知灯语集群实验 - 修复版本")
            print(f"节点: {self.config.node_name}")
            print("="*60)
            print("实验模式:")
            print("  1. 启动2D空间检测（多目标模式）")
            print("  2. 查看2D空间参数")
            print("  3. 调整2D参数")
            print("  4. 显示检测目标（多目标分析）")
            print("  q. 退出")
            print("="*60)

            try:
                choice = input("请选择: ").strip()

                if choice == '1':
                    self.start_2d_detection()
                elif choice == '2':
                    self.show_2d_params()
                elif choice == '3':
                    self.adjust_2d_params()
                elif choice == '4':
                    self.show_detected_targets_2d()
                elif choice.lower() == 'q':
                    print("退出实验")
                    break
                else:
                    print("无效选择，请重新输入")

            except KeyboardInterrupt:
                print("\n实验中断")
                break
