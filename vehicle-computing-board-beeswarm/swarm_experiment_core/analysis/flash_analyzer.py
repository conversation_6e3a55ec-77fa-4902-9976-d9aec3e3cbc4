#!/usr/bin/env python3
"""
灯语分析模块
负责分析目标的闪烁模式并映射到集群指令
"""

import rospy
from .flash_patterns import analyze_flash_patterns_v5_multi_target, map_patterns_to_numbers_multi_target

class FlashAnalyzer:
    """灯语分析器"""
    
    def __init__(self, config):
        self.config = config
        
    def analyze_flash_patterns(self, all_camera_detections):
        """分析所有摄像头的灯语模式"""
        rospy.loginfo("🔆 第一步：灯语分析...")
        valid_targets = []  # [(cam_name, track_id, pattern, number, behavior, priority)]
        
        # 指令优先级定义（数值越小优先级越高）
        COMMAND_PRIORITY = {
            "avoid": 1,
            "retreat": 2,
            "approach": 3,
            "follow": 4,
            "circle": 5,
            "align": 6,
            "parallel": 7,
            "stop": 8
        }

        for cam_name in all_camera_detections.keys():
            label_path = f"{self.config.config['base_path']}/{cam_name}"
            try:
                all_track_patterns = analyze_flash_patterns_v5_multi_target(
                    label_path, 
                    self.config.config['flash_num_frames'], 
                    start_from_frame=self.config.config['flash_start_frame'], 
                    threshold=self.config.config['flash_threshold']
                )
                track_numbers = map_patterns_to_numbers_multi_target(all_track_patterns)

                for track_id, patterns in all_track_patterns.items():
                    if track_id in track_numbers:
                        numbers = track_numbers[track_id]
                        for pattern, number in zip(patterns, numbers):
                            if pattern != "无闪烁" and number != -1 and number in self.config.SWARM_COMMANDS:
                                behavior = self.config.SWARM_COMMANDS[number]
                                priority = COMMAND_PRIORITY.get(behavior, 99)
                                valid_targets.append((cam_name, track_id, pattern, number, behavior, priority))
                                rospy.loginfo(f"   ✅ 发现有效指令: {cam_name} TrackID{track_id} → {behavior}")
            except Exception as e:
                rospy.logwarn(f"   ❌ {cam_name} 灯语分析失败: {e}")

        if not valid_targets:
            rospy.logwarn("❌ 没有检测到有效的集群指令")
            return []
            
        return valid_targets
    
    def get_best_target(self, valid_targets):
        """从有效目标中选择最优目标"""
        if not valid_targets:
            return None
            
        # 按优先级和距离排序（这里暂时只按优先级排序，距离在空间分析中处理）
        valid_targets.sort(key=lambda x: x[5])  # x[5]是priority
        return valid_targets[0]
