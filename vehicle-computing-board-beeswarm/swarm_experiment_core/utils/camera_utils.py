#!/usr/bin/env python3
"""
相机工具模块
从原 cal_vec.py 重构而来，提供更规范的命名和结构

功能：
- 相机内参矩阵创建
- 空间位置计算
- 方位角和距离计算
- 平面几何计算优化
"""

import os
import numpy as np
import math
from collections import defaultdict


def create_camera_matrix(f_x=381.36246688113556, f_y=None, c_x=320.5, c_y=180.5):
    """创建相机内参矩阵"""
    if f_y is None:
        f_y = f_x
    return np.array([
        [f_x, 0, c_x],
        [0, f_y, c_y],
        [0, 0, 1]
    ])


def calculate_azimuth_planar(x_pixel, camera_params, debug=False):
    """
    基于平面几何的方位角计算 - 针对同平面多机器人场景优化
    
    核心思想：
    1. 所有目标在同一水平面，摄像头固定高度
    2. 图像Y坐标基本固定，主要变化是X坐标  
    3. 水平方位角直接由X轴偏移决定
    
    :param x_pixel: 目标在图像中的X坐标
    :param camera_params: 相机参数字典 {'fx': fx, 'cx': cx}
    :param debug: 是否输出调试信息
    :return: 方位角（度，相对于相机光轴）
    """
    fx = camera_params['fx']
    cx = camera_params['cx']
    
    # 计算像素偏移
    dx = x_pixel - cx
    
    # 计算水平方位角（弧度）
    azimuth_rad = math.atan(dx / fx)
    
    # 转换为度数
    azimuth_deg = math.degrees(azimuth_rad)
    
    if debug:
        print(f"像素坐标: x={x_pixel}")
        print(f"像素偏移: dx={dx}")
        print(f"方位角: {azimuth_deg:.1f}°")
    
    return azimuth_deg


def calculate_distance_from_width(pixel_width, real_width, fx):
    """
    基于目标宽度计算距离
    
    :param pixel_width: 目标在图像中的像素宽度
    :param real_width: 目标的真实宽度（米）
    :param fx: 相机焦距（像素）
    :return: 距离（米）
    """
    if pixel_width <= 0:
        return float('inf')
    
    distance = (real_width * fx) / pixel_width
    return distance


def calculate_position_vector_planar(width, x_center, y_center, camera_matrix_inv, real_width):
    """
    基于平面几何计算目标的位置向量 - 修复版本

    专门针对同平面多机器人场景设计：
    1. 所有目标在同一水平面
    2. 主要关注水平距离和方位角
    3. 简化计算，提高精度

    注意：此函数接收的是像素坐标，不是归一化坐标！

    :param width: 目标在图像中的宽度（像素坐标）
    :param x_center: 目标中心X坐标（像素坐标）
    :param y_center: 目标中心Y坐标（像素坐标）
    :param camera_matrix_inv: 相机内参逆矩阵
    :param real_width: 目标真实宽度（米）
    :return: (position_vector, distance, theta, azimuth, elevation)
    """

    # 从逆矩阵中提取相机参数
    fx = 1.0 / camera_matrix_inv[0, 0]
    fy = 1.0 / camera_matrix_inv[1, 1]
    cx = -camera_matrix_inv[0, 2] * fx
    cy = -camera_matrix_inv[1, 2] * fy

    # 输入已经是像素坐标，无需转换
    x_pixel = x_center
    y_pixel = y_center
    width_pixel = width

    # 计算距离（基于宽度）
    distance = calculate_distance_from_width(width_pixel, real_width, fx)

    # 计算方位角（水平角度）
    camera_params = {'fx': fx, 'cx': cx}
    azimuth = calculate_azimuth_planar(x_pixel, camera_params)

    # 对于平面场景，俯仰角基本固定
    elevation = 0.0  # 简化为0度

    # 计算3D位置向量（相机坐标系）
    azimuth_rad = math.radians(azimuth)

    # 在水平面上的位置
    x_world = distance * math.sin(azimuth_rad)
    y_world = distance * math.cos(azimuth_rad)
    z_world = 0.0  # 同平面，高度差为0

    position_vector = np.array([x_world, y_world, z_world])

    # theta 是与光轴的夹角（对于平面场景，等于方位角的绝对值）
    theta = abs(azimuth)

    return position_vector, distance, theta, azimuth, elevation


def calculate_position_vector_3d(width, x_center, y_center, camera_matrix_inv, real_width):
    """
    完整的3D位置向量计算（保留原有功能）

    注意：此函数接收的是像素坐标，不是归一化坐标！

    :param width: 目标在图像中的宽度（像素坐标）
    :param x_center: 目标中心X坐标（像素坐标）
    :param y_center: 目标中心Y坐标（像素坐标）
    :param camera_matrix_inv: 相机内参逆矩阵
    :param real_width: 目标真实宽度（米）
    :return: (position_vector, distance, theta, azimuth, elevation)
    """

    # 从逆矩阵中提取相机参数
    fx = 1.0 / camera_matrix_inv[0, 0]
    fy = 1.0 / camera_matrix_inv[1, 1]
    cx = -camera_matrix_inv[0, 2] * fx
    cy = -camera_matrix_inv[1, 2] * fy

    # 输入已经是像素坐标，无需转换
    x_pixel = x_center
    y_pixel = y_center
    width_pixel = width
    
    # 计算距离
    distance = calculate_distance_from_width(width_pixel, real_width, fx)
    
    # 计算方向向量（归一化图像坐标）
    x_norm = (x_pixel - cx) / fx
    y_norm = (y_pixel - cy) / fy
    
    # 计算角度
    azimuth = math.degrees(math.atan2(x_norm, 1.0))
    elevation = math.degrees(math.atan2(-y_norm, 1.0))
    
    # 计算与光轴的夹角
    theta = math.degrees(math.atan(math.sqrt(x_norm**2 + y_norm**2)))
    
    # 计算3D位置向量
    direction_norm = math.sqrt(x_norm**2 + y_norm**2 + 1.0)
    x_world = distance * x_norm / direction_norm
    y_world = distance * 1.0 / direction_norm
    z_world = distance * (-y_norm) / direction_norm
    
    position_vector = np.array([x_world, y_world, z_world])
    
    return position_vector, distance, theta, azimuth, elevation


def batch_calculate_positions(detections, camera_matrix_inv, real_width):
    """
    批量计算多个目标的位置信息
    
    :param detections: 检测结果列表 [(x_center, y_center, width, height), ...]
    :param camera_matrix_inv: 相机内参逆矩阵
    :param real_width: 目标真实宽度
    :return: 位置信息列表
    """
    results = []
    
    for detection in detections:
        x_center, y_center, width, height = detection
        try:
            position_vector, distance, theta, azimuth, elevation = calculate_position_vector_planar(
                width, x_center, y_center, camera_matrix_inv, real_width
            )
            results.append({
                'position_vector': position_vector,
                'distance': distance,
                'theta': theta,
                'azimuth': azimuth,
                'elevation': elevation,
                'valid': True
            })
        except Exception as e:
            results.append({
                'position_vector': np.array([0, 0, 0]),
                'distance': 0,
                'theta': 0,
                'azimuth': 0,
                'elevation': 0,
                'valid': False,
                'error': str(e)
            })
    
    return results


def calculate_relative_position(pos1, pos2):
    """
    计算两个位置之间的相对位置
    
    :param pos1: 位置1 [x, y, z]
    :param pos2: 位置2 [x, y, z]
    :return: (relative_vector, distance, azimuth)
    """
    relative_vector = np.array(pos2) - np.array(pos1)
    distance = np.linalg.norm(relative_vector[:2])  # 只考虑水平距离
    
    if distance > 0:
        azimuth = math.degrees(math.atan2(relative_vector[0], relative_vector[1]))
    else:
        azimuth = 0
    
    return relative_vector, distance, azimuth


def filter_valid_detections(detections, min_distance=0.5, max_distance=20.0):
    """
    过滤有效的检测结果
    
    :param detections: 检测结果列表
    :param min_distance: 最小有效距离
    :param max_distance: 最大有效距离
    :return: 过滤后的检测结果
    """
    valid_detections = []
    
    for detection in detections:
        if detection.get('valid', False):
            distance = detection['distance']
            if min_distance <= distance <= max_distance:
                valid_detections.append(detection)
    
    return valid_detections


# 保持向后兼容的别名
def calculate_position_vector(width, x_center, y_center, camera_matrix_inv, real_width):
    """向后兼容的函数别名"""
    return calculate_position_vector_planar(width, x_center, y_center, camera_matrix_inv, real_width)
