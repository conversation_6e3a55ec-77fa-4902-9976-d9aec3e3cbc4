;; Auto-generated. Do not edit!


(when (boundp 'swarm_experiment::SetLight)
  (if (not (find-package "SWARM_EXPERIMENT"))
    (make-package "SWARM_EXPERIMENT"))
  (shadow 'SetLight (find-package "SWARM_EXPERIMENT")))
(unless (find-package "SWARM_EXPERIMENT::SETLIGHT")
  (make-package "SWARM_EXPERIMENT::SETLIGHT"))
(unless (find-package "SWARM_EXPERIMENT::SETLIGHTREQUEST")
  (make-package "SWARM_EXPERIMENT::SETLIGHTREQUEST"))
(unless (find-package "SWARM_EXPERIMENT::SETLIGHTRESPONSE")
  (make-package "SWARM_EXPERIMENT::SETLIGHTRESPONSE"))

(in-package "ROS")





(defclass swarm_experiment::SetLightRequest
  :super ros::object
  :slots (_light_semantic _duration ))

(defmethod swarm_experiment::SetLightRequest
  (:init
   (&key
    ((:light_semantic __light_semantic) "")
    ((:duration __duration) 0.0)
    )
   (send-super :init)
   (setq _light_semantic (string __light_semantic))
   (setq _duration (float __duration))
   self)
  (:light_semantic
   (&optional __light_semantic)
   (if __light_semantic (setq _light_semantic __light_semantic)) _light_semantic)
  (:duration
   (&optional __duration)
   (if __duration (setq _duration __duration)) _duration)
  (:serialization-length
   ()
   (+
    ;; string _light_semantic
    4 (length _light_semantic)
    ;; float32 _duration
    4
    ))
  (:serialize
   (&optional strm)
   (let ((s (if strm strm
              (make-string-output-stream (send self :serialization-length)))))
     ;; string _light_semantic
       (write-long (length _light_semantic) s) (princ _light_semantic s)
     ;; float32 _duration
       (sys::poke _duration (send s :buffer) (send s :count) :float) (incf (stream-count s) 4)
     ;;
     (if (null strm) (get-output-stream-string s))))
  (:deserialize
   (buf &optional (ptr- 0))
   ;; string _light_semantic
     (let (n) (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4) (setq _light_semantic (subseq buf ptr- (+ ptr- n))) (incf ptr- n))
   ;; float32 _duration
     (setq _duration (sys::peek buf ptr- :float)) (incf ptr- 4)
   ;;
   self)
  )

(defclass swarm_experiment::SetLightResponse
  :super ros::object
  :slots (_success _message ))

(defmethod swarm_experiment::SetLightResponse
  (:init
   (&key
    ((:success __success) nil)
    ((:message __message) "")
    )
   (send-super :init)
   (setq _success __success)
   (setq _message (string __message))
   self)
  (:success
   (&optional (__success :null))
   (if (not (eq __success :null)) (setq _success __success)) _success)
  (:message
   (&optional __message)
   (if __message (setq _message __message)) _message)
  (:serialization-length
   ()
   (+
    ;; bool _success
    1
    ;; string _message
    4 (length _message)
    ))
  (:serialize
   (&optional strm)
   (let ((s (if strm strm
              (make-string-output-stream (send self :serialization-length)))))
     ;; bool _success
       (if _success (write-byte -1 s) (write-byte 0 s))
     ;; string _message
       (write-long (length _message) s) (princ _message s)
     ;;
     (if (null strm) (get-output-stream-string s))))
  (:deserialize
   (buf &optional (ptr- 0))
   ;; bool _success
     (setq _success (not (= 0 (sys::peek buf ptr- :char)))) (incf ptr- 1)
   ;; string _message
     (let (n) (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4) (setq _message (subseq buf ptr- (+ ptr- n))) (incf ptr- n))
   ;;
   self)
  )

(defclass swarm_experiment::SetLight
  :super ros::object
  :slots ())

(setf (get swarm_experiment::SetLight :md5sum-) "e411def5f68acd335fe0434e90d7d05b")
(setf (get swarm_experiment::SetLight :datatype-) "swarm_experiment/SetLight")
(setf (get swarm_experiment::SetLight :request) swarm_experiment::SetLightRequest)
(setf (get swarm_experiment::SetLight :response) swarm_experiment::SetLightResponse)

(defmethod swarm_experiment::SetLightRequest
  (:response () (instance swarm_experiment::SetLightResponse :init)))

(setf (get swarm_experiment::SetLightRequest :md5sum-) "e411def5f68acd335fe0434e90d7d05b")
(setf (get swarm_experiment::SetLightRequest :datatype-) "swarm_experiment/SetLightRequest")
(setf (get swarm_experiment::SetLightRequest :definition-)
      "# 设置灯语服务定义
string light_semantic    # 灯语语义 (launch, gather, follow, disperse, finish, stop, emergency)
float32 duration         # 持续时间(秒)，0表示持续到下次切换
---
bool success            # 是否成功
string message          # 响应消息

")

(setf (get swarm_experiment::SetLightResponse :md5sum-) "e411def5f68acd335fe0434e90d7d05b")
(setf (get swarm_experiment::SetLightResponse :datatype-) "swarm_experiment/SetLightResponse")
(setf (get swarm_experiment::SetLightResponse :definition-)
      "# 设置灯语服务定义
string light_semantic    # 灯语语义 (launch, gather, follow, disperse, finish, stop, emergency)
float32 duration         # 持续时间(秒)，0表示持续到下次切换
---
bool success            # 是否成功
string message          # 响应消息

")



(provide :swarm_experiment/SetLight "e411def5f68acd335fe0434e90d7d05b")


