;; Auto-generated. Do not edit!


(when (boundp 'swarm_control::commander)
  (if (not (find-package "SWARM_CONTROL"))
    (make-package "SWARM_CONTROL"))
  (shadow 'commander (find-package "SWARM_CONTROL")))
(unless (find-package "SWARM_CONTROL::COMMANDER")
  (make-package "SWARM_CONTROL::COMMANDER"))
(unless (find-package "SWARM_CONTROL::COMMANDERREQUEST")
  (make-package "SWARM_CONTROL::COMMANDERREQUEST"))
(unless (find-package "SWARM_CONTROL::COMMANDERRESPONSE")
  (make-package "SWARM_CONTROL::COMMAN<PERSON><PERSON>RE<PERSON>ONSE"))

(in-package "ROS")





(defclass swarm_control::commanderRequest
  :super ros::object
  :slots (_command ))

(defmethod swarm_control::commanderRequest
  (:init
   (&key
    ((:command __command) "")
    )
   (send-super :init)
   (setq _command (string __command))
   self)
  (:command
   (&optional __command)
   (if __command (setq _command __command)) _command)
  (:serialization-length
   ()
   (+
    ;; string _command
    4 (length _command)
    ))
  (:serialize
   (&optional strm)
   (let ((s (if strm strm
              (make-string-output-stream (send self :serialization-length)))))
     ;; string _command
       (write-long (length _command) s) (princ _command s)
     ;;
     (if (null strm) (get-output-stream-string s))))
  (:deserialize
   (buf &optional (ptr- 0))
   ;; string _command
     (let (n) (setq n (sys::peek buf ptr- :integer)) (incf ptr- 4) (setq _command (subseq buf ptr- (+ ptr- n))) (incf ptr- n))
   ;;
   self)
  )

(defclass swarm_control::commanderResponse
  :super ros::object
  :slots (_success ))

(defmethod swarm_control::commanderResponse
  (:init
   (&key
    ((:success __success) nil)
    )
   (send-super :init)
   (setq _success __success)
   self)
  (:success
   (&optional (__success :null))
   (if (not (eq __success :null)) (setq _success __success)) _success)
  (:serialization-length
   ()
   (+
    ;; bool _success
    1
    ))
  (:serialize
   (&optional strm)
   (let ((s (if strm strm
              (make-string-output-stream (send self :serialization-length)))))
     ;; bool _success
       (if _success (write-byte -1 s) (write-byte 0 s))
     ;;
     (if (null strm) (get-output-stream-string s))))
  (:deserialize
   (buf &optional (ptr- 0))
   ;; bool _success
     (setq _success (not (= 0 (sys::peek buf ptr- :char)))) (incf ptr- 1)
   ;;
   self)
  )

(defclass swarm_control::commander
  :super ros::object
  :slots ())

(setf (get swarm_control::commander :md5sum-) "031d24522d462b2314fd1b6270670dd2")
(setf (get swarm_control::commander :datatype-) "swarm_control/commander")
(setf (get swarm_control::commander :request) swarm_control::commanderRequest)
(setf (get swarm_control::commander :response) swarm_control::commanderResponse)

(defmethod swarm_control::commanderRequest
  (:response () (instance swarm_control::commanderResponse :init)))

(setf (get swarm_control::commanderRequest :md5sum-) "031d24522d462b2314fd1b6270670dd2")
(setf (get swarm_control::commanderRequest :datatype-) "swarm_control/commanderRequest")
(setf (get swarm_control::commanderRequest :definition-)
      "string command  # \"AXX\"格式的命令，其中XX是两位数字
---
bool success  # 成功执行命令后返回true 
")

(setf (get swarm_control::commanderResponse :md5sum-) "031d24522d462b2314fd1b6270670dd2")
(setf (get swarm_control::commanderResponse :datatype-) "swarm_control/commanderResponse")
(setf (get swarm_control::commanderResponse :definition-)
      "string command  # \"AXX\"格式的命令，其中XX是两位数字
---
bool success  # 成功执行命令后返回true 
")



(provide :swarm_control/commander "031d24522d462b2314fd1b6270670dd2")


