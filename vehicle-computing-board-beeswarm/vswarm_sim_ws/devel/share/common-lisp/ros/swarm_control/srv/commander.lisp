; Auto-generated. Do not edit!


(cl:in-package swarm_control-srv)


;//! \htmlinclude commander-request.msg.html

(cl:defclass <commander-request> (roslisp-msg-protocol:ros-message)
  ((command
    :reader command
    :initarg :command
    :type cl:string
    :initform ""))
)

(cl:defclass commander-request (<commander-request>)
  ())

(cl:defmethod cl:initialize-instance :after ((m <commander-request>) cl:&rest args)
  (cl:declare (cl:ignorable args))
  (cl:unless (cl:typep m 'commander-request)
    (roslisp-msg-protocol:msg-deprecation-warning "using old message class name swarm_control-srv:<commander-request> is deprecated: use swarm_control-srv:commander-request instead.")))

(cl:ensure-generic-function 'command-val :lambda-list '(m))
(cl:defmethod command-val ((m <commander-request>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader swarm_control-srv:command-val is deprecated.  Use swarm_control-srv:command instead.")
  (command m))
(cl:defmethod roslisp-msg-protocol:serialize ((msg <commander-request>) ostream)
  "Serializes a message object of type '<commander-request>"
  (cl:let ((__ros_str_len (cl:length (cl:slot-value msg 'command))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __ros_str_len) ostream))
  (cl:map cl:nil #'(cl:lambda (c) (cl:write-byte (cl:char-code c) ostream)) (cl:slot-value msg 'command))
)
(cl:defmethod roslisp-msg-protocol:deserialize ((msg <commander-request>) istream)
  "Deserializes a message object of type '<commander-request>"
    (cl:let ((__ros_str_len 0))
      (cl:setf (cl:ldb (cl:byte 8 0) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:slot-value msg 'command) (cl:make-string __ros_str_len))
      (cl:dotimes (__ros_str_idx __ros_str_len msg)
        (cl:setf (cl:char (cl:slot-value msg 'command) __ros_str_idx) (cl:code-char (cl:read-byte istream)))))
  msg
)
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql '<commander-request>)))
  "Returns string type for a service object of type '<commander-request>"
  "swarm_control/commanderRequest")
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql 'commander-request)))
  "Returns string type for a service object of type 'commander-request"
  "swarm_control/commanderRequest")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql '<commander-request>)))
  "Returns md5sum for a message object of type '<commander-request>"
  "031d24522d462b2314fd1b6270670dd2")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql 'commander-request)))
  "Returns md5sum for a message object of type 'commander-request"
  "031d24522d462b2314fd1b6270670dd2")
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql '<commander-request>)))
  "Returns full string definition for message of type '<commander-request>"
  (cl:format cl:nil "string command  # \"AXX\"格式的命令，其中XX是两位数字~%~%~%"))
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql 'commander-request)))
  "Returns full string definition for message of type 'commander-request"
  (cl:format cl:nil "string command  # \"AXX\"格式的命令，其中XX是两位数字~%~%~%"))
(cl:defmethod roslisp-msg-protocol:serialization-length ((msg <commander-request>))
  (cl:+ 0
     4 (cl:length (cl:slot-value msg 'command))
))
(cl:defmethod roslisp-msg-protocol:ros-message-to-list ((msg <commander-request>))
  "Converts a ROS message object to a list"
  (cl:list 'commander-request
    (cl:cons ':command (command msg))
))
;//! \htmlinclude commander-response.msg.html

(cl:defclass <commander-response> (roslisp-msg-protocol:ros-message)
  ((success
    :reader success
    :initarg :success
    :type cl:boolean
    :initform cl:nil))
)

(cl:defclass commander-response (<commander-response>)
  ())

(cl:defmethod cl:initialize-instance :after ((m <commander-response>) cl:&rest args)
  (cl:declare (cl:ignorable args))
  (cl:unless (cl:typep m 'commander-response)
    (roslisp-msg-protocol:msg-deprecation-warning "using old message class name swarm_control-srv:<commander-response> is deprecated: use swarm_control-srv:commander-response instead.")))

(cl:ensure-generic-function 'success-val :lambda-list '(m))
(cl:defmethod success-val ((m <commander-response>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader swarm_control-srv:success-val is deprecated.  Use swarm_control-srv:success instead.")
  (success m))
(cl:defmethod roslisp-msg-protocol:serialize ((msg <commander-response>) ostream)
  "Serializes a message object of type '<commander-response>"
  (cl:write-byte (cl:ldb (cl:byte 8 0) (cl:if (cl:slot-value msg 'success) 1 0)) ostream)
)
(cl:defmethod roslisp-msg-protocol:deserialize ((msg <commander-response>) istream)
  "Deserializes a message object of type '<commander-response>"
    (cl:setf (cl:slot-value msg 'success) (cl:not (cl:zerop (cl:read-byte istream))))
  msg
)
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql '<commander-response>)))
  "Returns string type for a service object of type '<commander-response>"
  "swarm_control/commanderResponse")
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql 'commander-response)))
  "Returns string type for a service object of type 'commander-response"
  "swarm_control/commanderResponse")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql '<commander-response>)))
  "Returns md5sum for a message object of type '<commander-response>"
  "031d24522d462b2314fd1b6270670dd2")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql 'commander-response)))
  "Returns md5sum for a message object of type 'commander-response"
  "031d24522d462b2314fd1b6270670dd2")
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql '<commander-response>)))
  "Returns full string definition for message of type '<commander-response>"
  (cl:format cl:nil "bool success  # 成功执行命令后返回true ~%~%~%"))
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql 'commander-response)))
  "Returns full string definition for message of type 'commander-response"
  (cl:format cl:nil "bool success  # 成功执行命令后返回true ~%~%~%"))
(cl:defmethod roslisp-msg-protocol:serialization-length ((msg <commander-response>))
  (cl:+ 0
     1
))
(cl:defmethod roslisp-msg-protocol:ros-message-to-list ((msg <commander-response>))
  "Converts a ROS message object to a list"
  (cl:list 'commander-response
    (cl:cons ':success (success msg))
))
(cl:defmethod roslisp-msg-protocol:service-request-type ((msg (cl:eql 'commander)))
  'commander-request)
(cl:defmethod roslisp-msg-protocol:service-response-type ((msg (cl:eql 'commander)))
  'commander-response)
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql 'commander)))
  "Returns string type for a service object of type '<commander>"
  "swarm_control/commander")