; Auto-generated. Do not edit!


(cl:in-package swarm_experiment-srv)


;//! \htmlinclude SetLight-request.msg.html

(cl:defclass <SetLight-request> (roslisp-msg-protocol:ros-message)
  ((light_semantic
    :reader light_semantic
    :initarg :light_semantic
    :type cl:string
    :initform "")
   (duration
    :reader duration
    :initarg :duration
    :type cl:float
    :initform 0.0))
)

(cl:defclass SetLight-request (<SetLight-request>)
  ())

(cl:defmethod cl:initialize-instance :after ((m <SetLight-request>) cl:&rest args)
  (cl:declare (cl:ignorable args))
  (cl:unless (cl:typep m 'SetLight-request)
    (roslisp-msg-protocol:msg-deprecation-warning "using old message class name swarm_experiment-srv:<SetLight-request> is deprecated: use swarm_experiment-srv:SetLight-request instead.")))

(cl:ensure-generic-function 'light_semantic-val :lambda-list '(m))
(cl:defmethod light_semantic-val ((m <SetLight-request>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader swarm_experiment-srv:light_semantic-val is deprecated.  Use swarm_experiment-srv:light_semantic instead.")
  (light_semantic m))

(cl:ensure-generic-function 'duration-val :lambda-list '(m))
(cl:defmethod duration-val ((m <SetLight-request>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader swarm_experiment-srv:duration-val is deprecated.  Use swarm_experiment-srv:duration instead.")
  (duration m))
(cl:defmethod roslisp-msg-protocol:serialize ((msg <SetLight-request>) ostream)
  "Serializes a message object of type '<SetLight-request>"
  (cl:let ((__ros_str_len (cl:length (cl:slot-value msg 'light_semantic))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __ros_str_len) ostream))
  (cl:map cl:nil #'(cl:lambda (c) (cl:write-byte (cl:char-code c) ostream)) (cl:slot-value msg 'light_semantic))
  (cl:let ((bits (roslisp-utils:encode-single-float-bits (cl:slot-value msg 'duration))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) bits) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) bits) ostream))
)
(cl:defmethod roslisp-msg-protocol:deserialize ((msg <SetLight-request>) istream)
  "Deserializes a message object of type '<SetLight-request>"
    (cl:let ((__ros_str_len 0))
      (cl:setf (cl:ldb (cl:byte 8 0) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:slot-value msg 'light_semantic) (cl:make-string __ros_str_len))
      (cl:dotimes (__ros_str_idx __ros_str_len msg)
        (cl:setf (cl:char (cl:slot-value msg 'light_semantic) __ros_str_idx) (cl:code-char (cl:read-byte istream)))))
    (cl:let ((bits 0))
      (cl:setf (cl:ldb (cl:byte 8 0) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) bits) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) bits) (cl:read-byte istream))
    (cl:setf (cl:slot-value msg 'duration) (roslisp-utils:decode-single-float-bits bits)))
  msg
)
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql '<SetLight-request>)))
  "Returns string type for a service object of type '<SetLight-request>"
  "swarm_experiment/SetLightRequest")
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql 'SetLight-request)))
  "Returns string type for a service object of type 'SetLight-request"
  "swarm_experiment/SetLightRequest")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql '<SetLight-request>)))
  "Returns md5sum for a message object of type '<SetLight-request>"
  "e411def5f68acd335fe0434e90d7d05b")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql 'SetLight-request)))
  "Returns md5sum for a message object of type 'SetLight-request"
  "e411def5f68acd335fe0434e90d7d05b")
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql '<SetLight-request>)))
  "Returns full string definition for message of type '<SetLight-request>"
  (cl:format cl:nil "# 设置灯语服务定义~%string light_semantic    # 灯语语义 (launch, gather, follow, disperse, finish, stop, emergency)~%float32 duration         # 持续时间(秒)，0表示持续到下次切换~%~%~%"))
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql 'SetLight-request)))
  "Returns full string definition for message of type 'SetLight-request"
  (cl:format cl:nil "# 设置灯语服务定义~%string light_semantic    # 灯语语义 (launch, gather, follow, disperse, finish, stop, emergency)~%float32 duration         # 持续时间(秒)，0表示持续到下次切换~%~%~%"))
(cl:defmethod roslisp-msg-protocol:serialization-length ((msg <SetLight-request>))
  (cl:+ 0
     4 (cl:length (cl:slot-value msg 'light_semantic))
     4
))
(cl:defmethod roslisp-msg-protocol:ros-message-to-list ((msg <SetLight-request>))
  "Converts a ROS message object to a list"
  (cl:list 'SetLight-request
    (cl:cons ':light_semantic (light_semantic msg))
    (cl:cons ':duration (duration msg))
))
;//! \htmlinclude SetLight-response.msg.html

(cl:defclass <SetLight-response> (roslisp-msg-protocol:ros-message)
  ((success
    :reader success
    :initarg :success
    :type cl:boolean
    :initform cl:nil)
   (message
    :reader message
    :initarg :message
    :type cl:string
    :initform ""))
)

(cl:defclass SetLight-response (<SetLight-response>)
  ())

(cl:defmethod cl:initialize-instance :after ((m <SetLight-response>) cl:&rest args)
  (cl:declare (cl:ignorable args))
  (cl:unless (cl:typep m 'SetLight-response)
    (roslisp-msg-protocol:msg-deprecation-warning "using old message class name swarm_experiment-srv:<SetLight-response> is deprecated: use swarm_experiment-srv:SetLight-response instead.")))

(cl:ensure-generic-function 'success-val :lambda-list '(m))
(cl:defmethod success-val ((m <SetLight-response>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader swarm_experiment-srv:success-val is deprecated.  Use swarm_experiment-srv:success instead.")
  (success m))

(cl:ensure-generic-function 'message-val :lambda-list '(m))
(cl:defmethod message-val ((m <SetLight-response>))
  (roslisp-msg-protocol:msg-deprecation-warning "Using old-style slot reader swarm_experiment-srv:message-val is deprecated.  Use swarm_experiment-srv:message instead.")
  (message m))
(cl:defmethod roslisp-msg-protocol:serialize ((msg <SetLight-response>) ostream)
  "Serializes a message object of type '<SetLight-response>"
  (cl:write-byte (cl:ldb (cl:byte 8 0) (cl:if (cl:slot-value msg 'success) 1 0)) ostream)
  (cl:let ((__ros_str_len (cl:length (cl:slot-value msg 'message))))
    (cl:write-byte (cl:ldb (cl:byte 8 0) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 8) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 16) __ros_str_len) ostream)
    (cl:write-byte (cl:ldb (cl:byte 8 24) __ros_str_len) ostream))
  (cl:map cl:nil #'(cl:lambda (c) (cl:write-byte (cl:char-code c) ostream)) (cl:slot-value msg 'message))
)
(cl:defmethod roslisp-msg-protocol:deserialize ((msg <SetLight-response>) istream)
  "Deserializes a message object of type '<SetLight-response>"
    (cl:setf (cl:slot-value msg 'success) (cl:not (cl:zerop (cl:read-byte istream))))
    (cl:let ((__ros_str_len 0))
      (cl:setf (cl:ldb (cl:byte 8 0) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 8) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 16) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:ldb (cl:byte 8 24) __ros_str_len) (cl:read-byte istream))
      (cl:setf (cl:slot-value msg 'message) (cl:make-string __ros_str_len))
      (cl:dotimes (__ros_str_idx __ros_str_len msg)
        (cl:setf (cl:char (cl:slot-value msg 'message) __ros_str_idx) (cl:code-char (cl:read-byte istream)))))
  msg
)
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql '<SetLight-response>)))
  "Returns string type for a service object of type '<SetLight-response>"
  "swarm_experiment/SetLightResponse")
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql 'SetLight-response)))
  "Returns string type for a service object of type 'SetLight-response"
  "swarm_experiment/SetLightResponse")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql '<SetLight-response>)))
  "Returns md5sum for a message object of type '<SetLight-response>"
  "e411def5f68acd335fe0434e90d7d05b")
(cl:defmethod roslisp-msg-protocol:md5sum ((type (cl:eql 'SetLight-response)))
  "Returns md5sum for a message object of type 'SetLight-response"
  "e411def5f68acd335fe0434e90d7d05b")
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql '<SetLight-response>)))
  "Returns full string definition for message of type '<SetLight-response>"
  (cl:format cl:nil "bool success            # 是否成功~%string message          # 响应消息~%~%~%~%"))
(cl:defmethod roslisp-msg-protocol:message-definition ((type (cl:eql 'SetLight-response)))
  "Returns full string definition for message of type 'SetLight-response"
  (cl:format cl:nil "bool success            # 是否成功~%string message          # 响应消息~%~%~%~%"))
(cl:defmethod roslisp-msg-protocol:serialization-length ((msg <SetLight-response>))
  (cl:+ 0
     1
     4 (cl:length (cl:slot-value msg 'message))
))
(cl:defmethod roslisp-msg-protocol:ros-message-to-list ((msg <SetLight-response>))
  "Converts a ROS message object to a list"
  (cl:list 'SetLight-response
    (cl:cons ':success (success msg))
    (cl:cons ':message (message msg))
))
(cl:defmethod roslisp-msg-protocol:service-request-type ((msg (cl:eql 'SetLight)))
  'SetLight-request)
(cl:defmethod roslisp-msg-protocol:service-response-type ((msg (cl:eql 'SetLight)))
  'SetLight-response)
(cl:defmethod roslisp-msg-protocol:ros-datatype ((msg (cl:eql 'SetLight)))
  "Returns string type for a service object of type '<SetLight>"
  "swarm_experiment/SetLight")