// Generated by gencpp from file swarm_experiment/SwarmCommand.msg
// DO NOT EDIT!


#ifndef SWARM_EXPERIMENT_MESSAGE_SWARMCOMMAND_H
#define SWARM_EXPERIMENT_MESSAGE_SWARMCOMMAND_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <std_msgs/Header.h>

namespace swarm_experiment
{
template <class ContainerAllocator>
struct SwarmCommand_
{
  typedef SwarmCommand_<ContainerAllocator> Type;

  SwarmCommand_()
    : header()
    , command_name()
    , command_id(0)
    , target_distance(0.0)
    , target_azimuth(0.0)
    , camera_name()
    , track_id(0)  {
    }
  SwarmCommand_(const ContainerAllocator& _alloc)
    : header(_alloc)
    , command_name(_alloc)
    , command_id(0)
    , target_distance(0.0)
    , target_azimuth(0.0)
    , camera_name(_alloc)
    , track_id(0)  {
  (void)_alloc;
    }



   typedef  ::std_msgs::Header_<ContainerAllocator>  _header_type;
  _header_type header;

   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _command_name_type;
  _command_name_type command_name;

   typedef int32_t _command_id_type;
  _command_id_type command_id;

   typedef float _target_distance_type;
  _target_distance_type target_distance;

   typedef float _target_azimuth_type;
  _target_azimuth_type target_azimuth;

   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _camera_name_type;
  _camera_name_type camera_name;

   typedef int32_t _track_id_type;
  _track_id_type track_id;





  typedef boost::shared_ptr< ::swarm_experiment::SwarmCommand_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::swarm_experiment::SwarmCommand_<ContainerAllocator> const> ConstPtr;

}; // struct SwarmCommand_

typedef ::swarm_experiment::SwarmCommand_<std::allocator<void> > SwarmCommand;

typedef boost::shared_ptr< ::swarm_experiment::SwarmCommand > SwarmCommandPtr;
typedef boost::shared_ptr< ::swarm_experiment::SwarmCommand const> SwarmCommandConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::swarm_experiment::SwarmCommand_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::swarm_experiment::SwarmCommand_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::swarm_experiment::SwarmCommand_<ContainerAllocator1> & lhs, const ::swarm_experiment::SwarmCommand_<ContainerAllocator2> & rhs)
{
  return lhs.header == rhs.header &&
    lhs.command_name == rhs.command_name &&
    lhs.command_id == rhs.command_id &&
    lhs.target_distance == rhs.target_distance &&
    lhs.target_azimuth == rhs.target_azimuth &&
    lhs.camera_name == rhs.camera_name &&
    lhs.track_id == rhs.track_id;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::swarm_experiment::SwarmCommand_<ContainerAllocator1> & lhs, const ::swarm_experiment::SwarmCommand_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace swarm_experiment

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::swarm_experiment::SwarmCommand_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::swarm_experiment::SwarmCommand_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::swarm_experiment::SwarmCommand_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::swarm_experiment::SwarmCommand_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::swarm_experiment::SwarmCommand_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::swarm_experiment::SwarmCommand_<ContainerAllocator> const>
  : TrueType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::swarm_experiment::SwarmCommand_<ContainerAllocator> >
{
  static const char* value()
  {
    return "226c8ea4a9205e1877ea77c7bcebaad9";
  }

  static const char* value(const ::swarm_experiment::SwarmCommand_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x226c8ea4a9205e18ULL;
  static const uint64_t static_value2 = 0x77ea77c7bcebaad9ULL;
};

template<class ContainerAllocator>
struct DataType< ::swarm_experiment::SwarmCommand_<ContainerAllocator> >
{
  static const char* value()
  {
    return "swarm_experiment/SwarmCommand";
  }

  static const char* value(const ::swarm_experiment::SwarmCommand_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::swarm_experiment::SwarmCommand_<ContainerAllocator> >
{
  static const char* value()
  {
    return "# 群体指令消息\n"
"Header header\n"
"string command_name     # 指令名称\n"
"int32 command_id       # 指令ID\n"
"float32 target_distance # 目标距离\n"
"float32 target_azimuth  # 目标方位角\n"
"string camera_name     # 检测摄像头\n"
"int32 track_id         # 目标跟踪ID\n"
"\n"
"================================================================================\n"
"MSG: std_msgs/Header\n"
"# Standard metadata for higher-level stamped data types.\n"
"# This is generally used to communicate timestamped data \n"
"# in a particular coordinate frame.\n"
"# \n"
"# sequence ID: consecutively increasing ID \n"
"uint32 seq\n"
"#Two-integer timestamp that is expressed as:\n"
"# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')\n"
"# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')\n"
"# time-handling sugar is provided by the client library\n"
"time stamp\n"
"#Frame this data is associated with\n"
"string frame_id\n"
;
  }

  static const char* value(const ::swarm_experiment::SwarmCommand_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::swarm_experiment::SwarmCommand_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.header);
      stream.next(m.command_name);
      stream.next(m.command_id);
      stream.next(m.target_distance);
      stream.next(m.target_azimuth);
      stream.next(m.camera_name);
      stream.next(m.track_id);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct SwarmCommand_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::swarm_experiment::SwarmCommand_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::swarm_experiment::SwarmCommand_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "header: ";
    Printer< ::std_msgs::Header_<ContainerAllocator> >::stream(s, indent + "  ", v.header);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "command_name: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.command_name);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "command_id: ";
    Printer<int32_t>::stream(s, indent + "  ", v.command_id);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "target_distance: ";
    Printer<float>::stream(s, indent + "  ", v.target_distance);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "target_azimuth: ";
    Printer<float>::stream(s, indent + "  ", v.target_azimuth);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "camera_name: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.camera_name);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "track_id: ";
    Printer<int32_t>::stream(s, indent + "  ", v.track_id);
  }
};

} // namespace message_operations
} // namespace ros

#endif // SWARM_EXPERIMENT_MESSAGE_SWARMCOMMAND_H
