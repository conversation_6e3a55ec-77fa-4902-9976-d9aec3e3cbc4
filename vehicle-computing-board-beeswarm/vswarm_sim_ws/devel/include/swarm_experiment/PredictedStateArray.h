// Generated by gencpp from file swarm_experiment/PredictedStateArray.msg
// DO NOT EDIT!


#ifndef SWARM_EXPERIMENT_MESSAGE_PREDICTEDSTATEARRAY_H
#define SWARM_EXPERIMENT_MESSAGE_PREDICTEDSTATEARRAY_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <std_msgs/Header.h>
#include <swarm_experiment/PredictedState.h>

namespace swarm_experiment
{
template <class ContainerAllocator>
struct PredictedStateArray_
{
  typedef PredictedStateArray_<ContainerAllocator> Type;

  PredictedStateArray_()
    : header()
    , predictions()  {
    }
  PredictedStateArray_(const ContainerAllocator& _alloc)
    : header(_alloc)
    , predictions(_alloc)  {
  (void)_alloc;
    }



   typedef  ::std_msgs::Header_<ContainerAllocator>  _header_type;
  _header_type header;

   typedef std::vector< ::swarm_experiment::PredictedState_<ContainerAllocator> , typename std::allocator_traits<ContainerAllocator>::template rebind_alloc< ::swarm_experiment::PredictedState_<ContainerAllocator> >> _predictions_type;
  _predictions_type predictions;





  typedef boost::shared_ptr< ::swarm_experiment::PredictedStateArray_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::swarm_experiment::PredictedStateArray_<ContainerAllocator> const> ConstPtr;

}; // struct PredictedStateArray_

typedef ::swarm_experiment::PredictedStateArray_<std::allocator<void> > PredictedStateArray;

typedef boost::shared_ptr< ::swarm_experiment::PredictedStateArray > PredictedStateArrayPtr;
typedef boost::shared_ptr< ::swarm_experiment::PredictedStateArray const> PredictedStateArrayConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::swarm_experiment::PredictedStateArray_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::swarm_experiment::PredictedStateArray_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::swarm_experiment::PredictedStateArray_<ContainerAllocator1> & lhs, const ::swarm_experiment::PredictedStateArray_<ContainerAllocator2> & rhs)
{
  return lhs.header == rhs.header &&
    lhs.predictions == rhs.predictions;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::swarm_experiment::PredictedStateArray_<ContainerAllocator1> & lhs, const ::swarm_experiment::PredictedStateArray_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace swarm_experiment

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::swarm_experiment::PredictedStateArray_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::swarm_experiment::PredictedStateArray_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::swarm_experiment::PredictedStateArray_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::swarm_experiment::PredictedStateArray_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::swarm_experiment::PredictedStateArray_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::swarm_experiment::PredictedStateArray_<ContainerAllocator> const>
  : TrueType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::swarm_experiment::PredictedStateArray_<ContainerAllocator> >
{
  static const char* value()
  {
    return "14759438e05e18dc02da7298d31b61ac";
  }

  static const char* value(const ::swarm_experiment::PredictedStateArray_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x14759438e05e18dcULL;
  static const uint64_t static_value2 = 0x02da7298d31b61acULL;
};

template<class ContainerAllocator>
struct DataType< ::swarm_experiment::PredictedStateArray_<ContainerAllocator> >
{
  static const char* value()
  {
    return "swarm_experiment/PredictedStateArray";
  }

  static const char* value(const ::swarm_experiment::PredictedStateArray_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::swarm_experiment::PredictedStateArray_<ContainerAllocator> >
{
  static const char* value()
  {
    return "# 预测状态数组消息\n"
"Header header\n"
"PredictedState[] predictions\n"
"\n"
"================================================================================\n"
"MSG: std_msgs/Header\n"
"# Standard metadata for higher-level stamped data types.\n"
"# This is generally used to communicate timestamped data \n"
"# in a particular coordinate frame.\n"
"# \n"
"# sequence ID: consecutively increasing ID \n"
"uint32 seq\n"
"#Two-integer timestamp that is expressed as:\n"
"# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')\n"
"# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')\n"
"# time-handling sugar is provided by the client library\n"
"time stamp\n"
"#Frame this data is associated with\n"
"string frame_id\n"
"\n"
"================================================================================\n"
"MSG: swarm_experiment/PredictedState\n"
"# 预测状态消息\n"
"string entity_type      # 实体类型 (\"target\", \"self\", \"obstacle\")\n"
"string entity_id        # 实体ID\n"
"float64 predicted_time  # 预测时间戳\n"
"float32 position_x      # 预测X位置\n"
"float32 position_y      # 预测Y位置\n"
"float32 velocity_x      # 预测X速度\n"
"float32 velocity_y      # 预测Y速度\n"
"float32 heading         # 预测朝向(弧度)\n"
"float32 uncertainty_radius  # 不确定性半径\n"
"float32 confidence      # 预测置信度\n"
;
  }

  static const char* value(const ::swarm_experiment::PredictedStateArray_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::swarm_experiment::PredictedStateArray_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.header);
      stream.next(m.predictions);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct PredictedStateArray_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::swarm_experiment::PredictedStateArray_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::swarm_experiment::PredictedStateArray_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "header: ";
    Printer< ::std_msgs::Header_<ContainerAllocator> >::stream(s, indent + "  ", v.header);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "predictions: ";
    if (v.predictions.empty() || false)
      s << "[";
    for (size_t i = 0; i < v.predictions.size(); ++i)
    {
      if (false && i > 0)
        s << ", ";
      else if (!false)
        s << std::endl << indent << "  -";
      Printer< ::swarm_experiment::PredictedState_<ContainerAllocator> >::stream(s, false ? std::string() : indent + "    ", v.predictions[i]);
    }
    if (v.predictions.empty() || false)
      s << "]";
  }
};

} // namespace message_operations
} // namespace ros

#endif // SWARM_EXPERIMENT_MESSAGE_PREDICTEDSTATEARRAY_H
