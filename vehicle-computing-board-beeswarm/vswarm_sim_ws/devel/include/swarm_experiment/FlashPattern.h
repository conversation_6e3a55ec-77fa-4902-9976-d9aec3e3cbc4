// Generated by gencpp from file swarm_experiment/FlashPattern.msg
// DO NOT EDIT!


#ifndef SWARM_EXPERIMENT_MESSAGE_FLASHPATTERN_H
#define SWARM_EXPERIMENT_MESSAGE_FLASHPATTERN_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>


namespace swarm_experiment
{
template <class ContainerAllocator>
struct FlashPattern_
{
  typedef FlashPattern_<ContainerAllocator> Type;

  FlashPattern_()
    : camera_name()
    , track_id(0)
    , pattern()
    , command_id(0)
    , command_name()
    , confidence(0.0)
    , timestamp(0.0)  {
    }
  FlashPattern_(const ContainerAllocator& _alloc)
    : camera_name(_alloc)
    , track_id(0)
    , pattern(_alloc)
    , command_id(0)
    , command_name(_alloc)
    , confidence(0.0)
    , timestamp(0.0)  {
  (void)_alloc;
    }



   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _camera_name_type;
  _camera_name_type camera_name;

   typedef int32_t _track_id_type;
  _track_id_type track_id;

   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _pattern_type;
  _pattern_type pattern;

   typedef int32_t _command_id_type;
  _command_id_type command_id;

   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _command_name_type;
  _command_name_type command_name;

   typedef float _confidence_type;
  _confidence_type confidence;

   typedef double _timestamp_type;
  _timestamp_type timestamp;





  typedef boost::shared_ptr< ::swarm_experiment::FlashPattern_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::swarm_experiment::FlashPattern_<ContainerAllocator> const> ConstPtr;

}; // struct FlashPattern_

typedef ::swarm_experiment::FlashPattern_<std::allocator<void> > FlashPattern;

typedef boost::shared_ptr< ::swarm_experiment::FlashPattern > FlashPatternPtr;
typedef boost::shared_ptr< ::swarm_experiment::FlashPattern const> FlashPatternConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::swarm_experiment::FlashPattern_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::swarm_experiment::FlashPattern_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::swarm_experiment::FlashPattern_<ContainerAllocator1> & lhs, const ::swarm_experiment::FlashPattern_<ContainerAllocator2> & rhs)
{
  return lhs.camera_name == rhs.camera_name &&
    lhs.track_id == rhs.track_id &&
    lhs.pattern == rhs.pattern &&
    lhs.command_id == rhs.command_id &&
    lhs.command_name == rhs.command_name &&
    lhs.confidence == rhs.confidence &&
    lhs.timestamp == rhs.timestamp;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::swarm_experiment::FlashPattern_<ContainerAllocator1> & lhs, const ::swarm_experiment::FlashPattern_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace swarm_experiment

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::swarm_experiment::FlashPattern_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::swarm_experiment::FlashPattern_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::swarm_experiment::FlashPattern_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::swarm_experiment::FlashPattern_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::swarm_experiment::FlashPattern_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::swarm_experiment::FlashPattern_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::swarm_experiment::FlashPattern_<ContainerAllocator> >
{
  static const char* value()
  {
    return "7507664b7e76be6086707465365a01e1";
  }

  static const char* value(const ::swarm_experiment::FlashPattern_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x7507664b7e76be60ULL;
  static const uint64_t static_value2 = 0x86707465365a01e1ULL;
};

template<class ContainerAllocator>
struct DataType< ::swarm_experiment::FlashPattern_<ContainerAllocator> >
{
  static const char* value()
  {
    return "swarm_experiment/FlashPattern";
  }

  static const char* value(const ::swarm_experiment::FlashPattern_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::swarm_experiment::FlashPattern_<ContainerAllocator> >
{
  static const char* value()
  {
    return "# 闪烁模式消息\n"
"string camera_name      # 摄像头名称\n"
"int32 track_id         # 跟踪ID\n"
"string pattern         # 闪烁模式字符串\n"
"int32 command_id       # 指令ID\n"
"string command_name    # 指令名称\n"
"float32 confidence     # 置信度\n"
"float64 timestamp      # 时间戳\n"
;
  }

  static const char* value(const ::swarm_experiment::FlashPattern_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::swarm_experiment::FlashPattern_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.camera_name);
      stream.next(m.track_id);
      stream.next(m.pattern);
      stream.next(m.command_id);
      stream.next(m.command_name);
      stream.next(m.confidence);
      stream.next(m.timestamp);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct FlashPattern_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::swarm_experiment::FlashPattern_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::swarm_experiment::FlashPattern_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "camera_name: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.camera_name);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "track_id: ";
    Printer<int32_t>::stream(s, indent + "  ", v.track_id);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "pattern: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.pattern);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "command_id: ";
    Printer<int32_t>::stream(s, indent + "  ", v.command_id);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "command_name: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.command_name);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "confidence: ";
    Printer<float>::stream(s, indent + "  ", v.confidence);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "timestamp: ";
    Printer<double>::stream(s, indent + "  ", v.timestamp);
  }
};

} // namespace message_operations
} // namespace ros

#endif // SWARM_EXPERIMENT_MESSAGE_FLASHPATTERN_H
