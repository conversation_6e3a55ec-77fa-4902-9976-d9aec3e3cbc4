// Generated by gencpp from file swarm_experiment/DetectionArray.msg
// DO NOT EDIT!


#ifndef SWARM_EXPERIMENT_MESSAGE_DETECTIONARRAY_H
#define SWARM_EXPERIMENT_MESSAGE_DETECTIONARRAY_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <std_msgs/Header.h>
#include <swarm_experiment/DetectionResult.h>

namespace swarm_experiment
{
template <class ContainerAllocator>
struct DetectionArray_
{
  typedef DetectionArray_<ContainerAllocator> Type;

  DetectionArray_()
    : header()
    , detections()  {
    }
  DetectionArray_(const ContainerAllocator& _alloc)
    : header(_alloc)
    , detections(_alloc)  {
  (void)_alloc;
    }



   typedef  ::std_msgs::Header_<ContainerAllocator>  _header_type;
  _header_type header;

   typedef std::vector< ::swarm_experiment::DetectionResult_<ContainerAllocator> , typename std::allocator_traits<ContainerAllocator>::template rebind_alloc< ::swarm_experiment::DetectionResult_<ContainerAllocator> >> _detections_type;
  _detections_type detections;





  typedef boost::shared_ptr< ::swarm_experiment::DetectionArray_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::swarm_experiment::DetectionArray_<ContainerAllocator> const> ConstPtr;

}; // struct DetectionArray_

typedef ::swarm_experiment::DetectionArray_<std::allocator<void> > DetectionArray;

typedef boost::shared_ptr< ::swarm_experiment::DetectionArray > DetectionArrayPtr;
typedef boost::shared_ptr< ::swarm_experiment::DetectionArray const> DetectionArrayConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::swarm_experiment::DetectionArray_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::swarm_experiment::DetectionArray_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::swarm_experiment::DetectionArray_<ContainerAllocator1> & lhs, const ::swarm_experiment::DetectionArray_<ContainerAllocator2> & rhs)
{
  return lhs.header == rhs.header &&
    lhs.detections == rhs.detections;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::swarm_experiment::DetectionArray_<ContainerAllocator1> & lhs, const ::swarm_experiment::DetectionArray_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace swarm_experiment

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::swarm_experiment::DetectionArray_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::swarm_experiment::DetectionArray_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::swarm_experiment::DetectionArray_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::swarm_experiment::DetectionArray_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::swarm_experiment::DetectionArray_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::swarm_experiment::DetectionArray_<ContainerAllocator> const>
  : TrueType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::swarm_experiment::DetectionArray_<ContainerAllocator> >
{
  static const char* value()
  {
    return "90e55e238c10a41be4639e4ab0c668e2";
  }

  static const char* value(const ::swarm_experiment::DetectionArray_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x90e55e238c10a41bULL;
  static const uint64_t static_value2 = 0xe4639e4ab0c668e2ULL;
};

template<class ContainerAllocator>
struct DataType< ::swarm_experiment::DetectionArray_<ContainerAllocator> >
{
  static const char* value()
  {
    return "swarm_experiment/DetectionArray";
  }

  static const char* value(const ::swarm_experiment::DetectionArray_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::swarm_experiment::DetectionArray_<ContainerAllocator> >
{
  static const char* value()
  {
    return "# 检测结果数组消息\n"
"Header header\n"
"DetectionResult[] detections\n"
"\n"
"================================================================================\n"
"MSG: std_msgs/Header\n"
"# Standard metadata for higher-level stamped data types.\n"
"# This is generally used to communicate timestamped data \n"
"# in a particular coordinate frame.\n"
"# \n"
"# sequence ID: consecutively increasing ID \n"
"uint32 seq\n"
"#Two-integer timestamp that is expressed as:\n"
"# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')\n"
"# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')\n"
"# time-handling sugar is provided by the client library\n"
"time stamp\n"
"#Frame this data is associated with\n"
"string frame_id\n"
"\n"
"================================================================================\n"
"MSG: swarm_experiment/DetectionResult\n"
"# 单个检测结果消息\n"
"string camera_name      # 摄像头名称\n"
"int32 track_id         # 跟踪ID\n"
"int32 class_id         # 类别ID\n"
"float32 x_center       # 中心点X坐标(像素)\n"
"float32 y_center       # 中心点Y坐标(像素)\n"
"float32 width          # 宽度(像素)\n"
"float32 height         # 高度(像素)\n"
"float32 confidence     # 置信度\n"
"float64 timestamp      # 时间戳\n"
;
  }

  static const char* value(const ::swarm_experiment::DetectionArray_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::swarm_experiment::DetectionArray_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.header);
      stream.next(m.detections);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct DetectionArray_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::swarm_experiment::DetectionArray_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::swarm_experiment::DetectionArray_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "header: ";
    Printer< ::std_msgs::Header_<ContainerAllocator> >::stream(s, indent + "  ", v.header);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "detections: ";
    if (v.detections.empty() || false)
      s << "[";
    for (size_t i = 0; i < v.detections.size(); ++i)
    {
      if (false && i > 0)
        s << ", ";
      else if (!false)
        s << std::endl << indent << "  -";
      Printer< ::swarm_experiment::DetectionResult_<ContainerAllocator> >::stream(s, false ? std::string() : indent + "    ", v.detections[i]);
    }
    if (v.detections.empty() || false)
      s << "]";
  }
};

} // namespace message_operations
} // namespace ros

#endif // SWARM_EXPERIMENT_MESSAGE_DETECTIONARRAY_H
