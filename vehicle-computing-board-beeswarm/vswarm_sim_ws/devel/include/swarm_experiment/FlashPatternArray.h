// Generated by gencpp from file swarm_experiment/FlashPatternArray.msg
// DO NOT EDIT!


#ifndef SWARM_EXPERIMENT_MESSAGE_FLASHPATTERNARRAY_H
#define SWARM_EXPERIMENT_MESSAGE_FLASHPATTERNARRAY_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <std_msgs/Header.h>
#include <swarm_experiment/FlashPattern.h>

namespace swarm_experiment
{
template <class ContainerAllocator>
struct FlashPatternArray_
{
  typedef FlashPatternArray_<ContainerAllocator> Type;

  FlashPatternArray_()
    : header()
    , patterns()  {
    }
  FlashPatternArray_(const ContainerAllocator& _alloc)
    : header(_alloc)
    , patterns(_alloc)  {
  (void)_alloc;
    }



   typedef  ::std_msgs::Header_<ContainerAllocator>  _header_type;
  _header_type header;

   typedef std::vector< ::swarm_experiment::FlashPattern_<ContainerAllocator> , typename std::allocator_traits<ContainerAllocator>::template rebind_alloc< ::swarm_experiment::FlashPattern_<ContainerAllocator> >> _patterns_type;
  _patterns_type patterns;





  typedef boost::shared_ptr< ::swarm_experiment::FlashPatternArray_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::swarm_experiment::FlashPatternArray_<ContainerAllocator> const> ConstPtr;

}; // struct FlashPatternArray_

typedef ::swarm_experiment::FlashPatternArray_<std::allocator<void> > FlashPatternArray;

typedef boost::shared_ptr< ::swarm_experiment::FlashPatternArray > FlashPatternArrayPtr;
typedef boost::shared_ptr< ::swarm_experiment::FlashPatternArray const> FlashPatternArrayConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::swarm_experiment::FlashPatternArray_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::swarm_experiment::FlashPatternArray_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::swarm_experiment::FlashPatternArray_<ContainerAllocator1> & lhs, const ::swarm_experiment::FlashPatternArray_<ContainerAllocator2> & rhs)
{
  return lhs.header == rhs.header &&
    lhs.patterns == rhs.patterns;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::swarm_experiment::FlashPatternArray_<ContainerAllocator1> & lhs, const ::swarm_experiment::FlashPatternArray_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace swarm_experiment

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::swarm_experiment::FlashPatternArray_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::swarm_experiment::FlashPatternArray_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::swarm_experiment::FlashPatternArray_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::swarm_experiment::FlashPatternArray_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::swarm_experiment::FlashPatternArray_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::swarm_experiment::FlashPatternArray_<ContainerAllocator> const>
  : TrueType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::swarm_experiment::FlashPatternArray_<ContainerAllocator> >
{
  static const char* value()
  {
    return "84ba5af72d6f1587e637c0529ef3c040";
  }

  static const char* value(const ::swarm_experiment::FlashPatternArray_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x84ba5af72d6f1587ULL;
  static const uint64_t static_value2 = 0xe637c0529ef3c040ULL;
};

template<class ContainerAllocator>
struct DataType< ::swarm_experiment::FlashPatternArray_<ContainerAllocator> >
{
  static const char* value()
  {
    return "swarm_experiment/FlashPatternArray";
  }

  static const char* value(const ::swarm_experiment::FlashPatternArray_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::swarm_experiment::FlashPatternArray_<ContainerAllocator> >
{
  static const char* value()
  {
    return "# 闪烁模式数组消息\n"
"Header header\n"
"FlashPattern[] patterns\n"
"\n"
"================================================================================\n"
"MSG: std_msgs/Header\n"
"# Standard metadata for higher-level stamped data types.\n"
"# This is generally used to communicate timestamped data \n"
"# in a particular coordinate frame.\n"
"# \n"
"# sequence ID: consecutively increasing ID \n"
"uint32 seq\n"
"#Two-integer timestamp that is expressed as:\n"
"# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')\n"
"# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')\n"
"# time-handling sugar is provided by the client library\n"
"time stamp\n"
"#Frame this data is associated with\n"
"string frame_id\n"
"\n"
"================================================================================\n"
"MSG: swarm_experiment/FlashPattern\n"
"# 闪烁模式消息\n"
"string camera_name      # 摄像头名称\n"
"int32 track_id         # 跟踪ID\n"
"string pattern         # 闪烁模式字符串\n"
"int32 command_id       # 指令ID\n"
"string command_name    # 指令名称\n"
"float32 confidence     # 置信度\n"
"float64 timestamp      # 时间戳\n"
;
  }

  static const char* value(const ::swarm_experiment::FlashPatternArray_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::swarm_experiment::FlashPatternArray_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.header);
      stream.next(m.patterns);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct FlashPatternArray_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::swarm_experiment::FlashPatternArray_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::swarm_experiment::FlashPatternArray_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "header: ";
    Printer< ::std_msgs::Header_<ContainerAllocator> >::stream(s, indent + "  ", v.header);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "patterns: ";
    if (v.patterns.empty() || false)
      s << "[";
    for (size_t i = 0; i < v.patterns.size(); ++i)
    {
      if (false && i > 0)
        s << ", ";
      else if (!false)
        s << std::endl << indent << "  -";
      Printer< ::swarm_experiment::FlashPattern_<ContainerAllocator> >::stream(s, false ? std::string() : indent + "    ", v.patterns[i]);
    }
    if (v.patterns.empty() || false)
      s << "]";
  }
};

} // namespace message_operations
} // namespace ros

#endif // SWARM_EXPERIMENT_MESSAGE_FLASHPATTERNARRAY_H
