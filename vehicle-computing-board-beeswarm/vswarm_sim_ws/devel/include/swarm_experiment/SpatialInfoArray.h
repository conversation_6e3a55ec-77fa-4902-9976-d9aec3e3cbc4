// Generated by gencpp from file swarm_experiment/SpatialInfoArray.msg
// DO NOT EDIT!


#ifndef SWARM_EXPERIMENT_MESSAGE_SPATIALINFOARRAY_H
#define SWARM_EXPERIMENT_MESSAGE_SPATIALINFOARRAY_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <std_msgs/Header.h>
#include <swarm_experiment/SpatialInfo.h>

namespace swarm_experiment
{
template <class ContainerAllocator>
struct SpatialInfoArray_
{
  typedef SpatialInfoArray_<ContainerAllocator> Type;

  SpatialInfoArray_()
    : header()
    , spatial_infos()  {
    }
  SpatialInfoArray_(const ContainerAllocator& _alloc)
    : header(_alloc)
    , spatial_infos(_alloc)  {
  (void)_alloc;
    }



   typedef  ::std_msgs::Header_<ContainerAllocator>  _header_type;
  _header_type header;

   typedef std::vector< ::swarm_experiment::SpatialInfo_<ContainerAllocator> , typename std::allocator_traits<ContainerAllocator>::template rebind_alloc< ::swarm_experiment::SpatialInfo_<ContainerAllocator> >> _spatial_infos_type;
  _spatial_infos_type spatial_infos;





  typedef boost::shared_ptr< ::swarm_experiment::SpatialInfoArray_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::swarm_experiment::SpatialInfoArray_<ContainerAllocator> const> ConstPtr;

}; // struct SpatialInfoArray_

typedef ::swarm_experiment::SpatialInfoArray_<std::allocator<void> > SpatialInfoArray;

typedef boost::shared_ptr< ::swarm_experiment::SpatialInfoArray > SpatialInfoArrayPtr;
typedef boost::shared_ptr< ::swarm_experiment::SpatialInfoArray const> SpatialInfoArrayConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::swarm_experiment::SpatialInfoArray_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::swarm_experiment::SpatialInfoArray_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::swarm_experiment::SpatialInfoArray_<ContainerAllocator1> & lhs, const ::swarm_experiment::SpatialInfoArray_<ContainerAllocator2> & rhs)
{
  return lhs.header == rhs.header &&
    lhs.spatial_infos == rhs.spatial_infos;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::swarm_experiment::SpatialInfoArray_<ContainerAllocator1> & lhs, const ::swarm_experiment::SpatialInfoArray_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace swarm_experiment

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::swarm_experiment::SpatialInfoArray_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::swarm_experiment::SpatialInfoArray_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::swarm_experiment::SpatialInfoArray_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::swarm_experiment::SpatialInfoArray_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::swarm_experiment::SpatialInfoArray_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::swarm_experiment::SpatialInfoArray_<ContainerAllocator> const>
  : TrueType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::swarm_experiment::SpatialInfoArray_<ContainerAllocator> >
{
  static const char* value()
  {
    return "4f6188ec05f40366795c89f368d12cd6";
  }

  static const char* value(const ::swarm_experiment::SpatialInfoArray_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x4f6188ec05f40366ULL;
  static const uint64_t static_value2 = 0x795c89f368d12cd6ULL;
};

template<class ContainerAllocator>
struct DataType< ::swarm_experiment::SpatialInfoArray_<ContainerAllocator> >
{
  static const char* value()
  {
    return "swarm_experiment/SpatialInfoArray";
  }

  static const char* value(const ::swarm_experiment::SpatialInfoArray_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::swarm_experiment::SpatialInfoArray_<ContainerAllocator> >
{
  static const char* value()
  {
    return "# 空间信息数组消息\n"
"Header header\n"
"SpatialInfo[] spatial_infos\n"
"\n"
"================================================================================\n"
"MSG: std_msgs/Header\n"
"# Standard metadata for higher-level stamped data types.\n"
"# This is generally used to communicate timestamped data \n"
"# in a particular coordinate frame.\n"
"# \n"
"# sequence ID: consecutively increasing ID \n"
"uint32 seq\n"
"#Two-integer timestamp that is expressed as:\n"
"# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')\n"
"# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')\n"
"# time-handling sugar is provided by the client library\n"
"time stamp\n"
"#Frame this data is associated with\n"
"string frame_id\n"
"\n"
"================================================================================\n"
"MSG: swarm_experiment/SpatialInfo\n"
"# 空间信息消息\n"
"string camera_name      # 摄像头名称\n"
"int32 track_id         # 跟踪ID\n"
"float32 distance       # 距离(米)\n"
"float32 azimuth        # 相对摄像头方位角(度)\n"
"float32 world_azimuth  # 世界坐标系方位角(度)\n"
"float32 position_x     # X位置(米)\n"
"float32 position_y     # Y位置(米)\n"
"float32 confidence     # 置信度\n"
"int32 detection_count  # 检测次数\n"
"float64 timestamp      # 时间戳\n"
;
  }

  static const char* value(const ::swarm_experiment::SpatialInfoArray_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::swarm_experiment::SpatialInfoArray_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.header);
      stream.next(m.spatial_infos);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct SpatialInfoArray_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::swarm_experiment::SpatialInfoArray_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::swarm_experiment::SpatialInfoArray_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "header: ";
    Printer< ::std_msgs::Header_<ContainerAllocator> >::stream(s, indent + "  ", v.header);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "spatial_infos: ";
    if (v.spatial_infos.empty() || false)
      s << "[";
    for (size_t i = 0; i < v.spatial_infos.size(); ++i)
    {
      if (false && i > 0)
        s << ", ";
      else if (!false)
        s << std::endl << indent << "  -";
      Printer< ::swarm_experiment::SpatialInfo_<ContainerAllocator> >::stream(s, false ? std::string() : indent + "    ", v.spatial_infos[i]);
    }
    if (v.spatial_infos.empty() || false)
      s << "]";
  }
};

} // namespace message_operations
} // namespace ros

#endif // SWARM_EXPERIMENT_MESSAGE_SPATIALINFOARRAY_H
