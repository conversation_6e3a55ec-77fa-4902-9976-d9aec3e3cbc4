// Generated by gencpp from file swarm_experiment/DetectionResult.msg
// DO NOT EDIT!


#ifndef SWARM_EXPERIMENT_MESSAGE_DETECTIONRESULT_H
#define SWARM_EXPERIMENT_MESSAGE_DETECTIONRESULT_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>


namespace swarm_experiment
{
template <class ContainerAllocator>
struct DetectionResult_
{
  typedef DetectionResult_<ContainerAllocator> Type;

  DetectionResult_()
    : camera_name()
    , track_id(0)
    , class_id(0)
    , x_center(0.0)
    , y_center(0.0)
    , width(0.0)
    , height(0.0)
    , confidence(0.0)
    , timestamp(0.0)  {
    }
  DetectionResult_(const ContainerAllocator& _alloc)
    : camera_name(_alloc)
    , track_id(0)
    , class_id(0)
    , x_center(0.0)
    , y_center(0.0)
    , width(0.0)
    , height(0.0)
    , confidence(0.0)
    , timestamp(0.0)  {
  (void)_alloc;
    }



   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _camera_name_type;
  _camera_name_type camera_name;

   typedef int32_t _track_id_type;
  _track_id_type track_id;

   typedef int32_t _class_id_type;
  _class_id_type class_id;

   typedef float _x_center_type;
  _x_center_type x_center;

   typedef float _y_center_type;
  _y_center_type y_center;

   typedef float _width_type;
  _width_type width;

   typedef float _height_type;
  _height_type height;

   typedef float _confidence_type;
  _confidence_type confidence;

   typedef double _timestamp_type;
  _timestamp_type timestamp;





  typedef boost::shared_ptr< ::swarm_experiment::DetectionResult_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::swarm_experiment::DetectionResult_<ContainerAllocator> const> ConstPtr;

}; // struct DetectionResult_

typedef ::swarm_experiment::DetectionResult_<std::allocator<void> > DetectionResult;

typedef boost::shared_ptr< ::swarm_experiment::DetectionResult > DetectionResultPtr;
typedef boost::shared_ptr< ::swarm_experiment::DetectionResult const> DetectionResultConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::swarm_experiment::DetectionResult_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::swarm_experiment::DetectionResult_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::swarm_experiment::DetectionResult_<ContainerAllocator1> & lhs, const ::swarm_experiment::DetectionResult_<ContainerAllocator2> & rhs)
{
  return lhs.camera_name == rhs.camera_name &&
    lhs.track_id == rhs.track_id &&
    lhs.class_id == rhs.class_id &&
    lhs.x_center == rhs.x_center &&
    lhs.y_center == rhs.y_center &&
    lhs.width == rhs.width &&
    lhs.height == rhs.height &&
    lhs.confidence == rhs.confidence &&
    lhs.timestamp == rhs.timestamp;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::swarm_experiment::DetectionResult_<ContainerAllocator1> & lhs, const ::swarm_experiment::DetectionResult_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace swarm_experiment

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::swarm_experiment::DetectionResult_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::swarm_experiment::DetectionResult_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::swarm_experiment::DetectionResult_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::swarm_experiment::DetectionResult_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::swarm_experiment::DetectionResult_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::swarm_experiment::DetectionResult_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::swarm_experiment::DetectionResult_<ContainerAllocator> >
{
  static const char* value()
  {
    return "fe795fc8505abafd40c89b9248af0245";
  }

  static const char* value(const ::swarm_experiment::DetectionResult_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0xfe795fc8505abafdULL;
  static const uint64_t static_value2 = 0x40c89b9248af0245ULL;
};

template<class ContainerAllocator>
struct DataType< ::swarm_experiment::DetectionResult_<ContainerAllocator> >
{
  static const char* value()
  {
    return "swarm_experiment/DetectionResult";
  }

  static const char* value(const ::swarm_experiment::DetectionResult_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::swarm_experiment::DetectionResult_<ContainerAllocator> >
{
  static const char* value()
  {
    return "# 单个检测结果消息\n"
"string camera_name      # 摄像头名称\n"
"int32 track_id         # 跟踪ID\n"
"int32 class_id         # 类别ID\n"
"float32 x_center       # 中心点X坐标(像素)\n"
"float32 y_center       # 中心点Y坐标(像素)\n"
"float32 width          # 宽度(像素)\n"
"float32 height         # 高度(像素)\n"
"float32 confidence     # 置信度\n"
"float64 timestamp      # 时间戳\n"
;
  }

  static const char* value(const ::swarm_experiment::DetectionResult_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::swarm_experiment::DetectionResult_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.camera_name);
      stream.next(m.track_id);
      stream.next(m.class_id);
      stream.next(m.x_center);
      stream.next(m.y_center);
      stream.next(m.width);
      stream.next(m.height);
      stream.next(m.confidence);
      stream.next(m.timestamp);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct DetectionResult_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::swarm_experiment::DetectionResult_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::swarm_experiment::DetectionResult_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "camera_name: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.camera_name);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "track_id: ";
    Printer<int32_t>::stream(s, indent + "  ", v.track_id);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "class_id: ";
    Printer<int32_t>::stream(s, indent + "  ", v.class_id);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "x_center: ";
    Printer<float>::stream(s, indent + "  ", v.x_center);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "y_center: ";
    Printer<float>::stream(s, indent + "  ", v.y_center);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "width: ";
    Printer<float>::stream(s, indent + "  ", v.width);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "height: ";
    Printer<float>::stream(s, indent + "  ", v.height);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "confidence: ";
    Printer<float>::stream(s, indent + "  ", v.confidence);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "timestamp: ";
    Printer<double>::stream(s, indent + "  ", v.timestamp);
  }
};

} // namespace message_operations
} // namespace ros

#endif // SWARM_EXPERIMENT_MESSAGE_DETECTIONRESULT_H
