// Generated by gencpp from file swarm_experiment/SpatialInfo.msg
// DO NOT EDIT!


#ifndef SWARM_EXPERIMENT_MESSAGE_SPATIALINFO_H
#define SWARM_EXPERIMENT_MESSAGE_SPATIALINFO_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>


namespace swarm_experiment
{
template <class ContainerAllocator>
struct SpatialInfo_
{
  typedef SpatialInfo_<ContainerAllocator> Type;

  SpatialInfo_()
    : camera_name()
    , track_id(0)
    , distance(0.0)
    , azimuth(0.0)
    , world_azimuth(0.0)
    , position_x(0.0)
    , position_y(0.0)
    , confidence(0.0)
    , detection_count(0)
    , timestamp(0.0)  {
    }
  SpatialInfo_(const ContainerAllocator& _alloc)
    : camera_name(_alloc)
    , track_id(0)
    , distance(0.0)
    , azimuth(0.0)
    , world_azimuth(0.0)
    , position_x(0.0)
    , position_y(0.0)
    , confidence(0.0)
    , detection_count(0)
    , timestamp(0.0)  {
  (void)_alloc;
    }



   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _camera_name_type;
  _camera_name_type camera_name;

   typedef int32_t _track_id_type;
  _track_id_type track_id;

   typedef float _distance_type;
  _distance_type distance;

   typedef float _azimuth_type;
  _azimuth_type azimuth;

   typedef float _world_azimuth_type;
  _world_azimuth_type world_azimuth;

   typedef float _position_x_type;
  _position_x_type position_x;

   typedef float _position_y_type;
  _position_y_type position_y;

   typedef float _confidence_type;
  _confidence_type confidence;

   typedef int32_t _detection_count_type;
  _detection_count_type detection_count;

   typedef double _timestamp_type;
  _timestamp_type timestamp;





  typedef boost::shared_ptr< ::swarm_experiment::SpatialInfo_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::swarm_experiment::SpatialInfo_<ContainerAllocator> const> ConstPtr;

}; // struct SpatialInfo_

typedef ::swarm_experiment::SpatialInfo_<std::allocator<void> > SpatialInfo;

typedef boost::shared_ptr< ::swarm_experiment::SpatialInfo > SpatialInfoPtr;
typedef boost::shared_ptr< ::swarm_experiment::SpatialInfo const> SpatialInfoConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::swarm_experiment::SpatialInfo_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::swarm_experiment::SpatialInfo_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::swarm_experiment::SpatialInfo_<ContainerAllocator1> & lhs, const ::swarm_experiment::SpatialInfo_<ContainerAllocator2> & rhs)
{
  return lhs.camera_name == rhs.camera_name &&
    lhs.track_id == rhs.track_id &&
    lhs.distance == rhs.distance &&
    lhs.azimuth == rhs.azimuth &&
    lhs.world_azimuth == rhs.world_azimuth &&
    lhs.position_x == rhs.position_x &&
    lhs.position_y == rhs.position_y &&
    lhs.confidence == rhs.confidence &&
    lhs.detection_count == rhs.detection_count &&
    lhs.timestamp == rhs.timestamp;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::swarm_experiment::SpatialInfo_<ContainerAllocator1> & lhs, const ::swarm_experiment::SpatialInfo_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace swarm_experiment

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::swarm_experiment::SpatialInfo_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::swarm_experiment::SpatialInfo_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::swarm_experiment::SpatialInfo_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::swarm_experiment::SpatialInfo_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::swarm_experiment::SpatialInfo_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::swarm_experiment::SpatialInfo_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::swarm_experiment::SpatialInfo_<ContainerAllocator> >
{
  static const char* value()
  {
    return "5fa3fd05601edc681910c8712d5594b5";
  }

  static const char* value(const ::swarm_experiment::SpatialInfo_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x5fa3fd05601edc68ULL;
  static const uint64_t static_value2 = 0x1910c8712d5594b5ULL;
};

template<class ContainerAllocator>
struct DataType< ::swarm_experiment::SpatialInfo_<ContainerAllocator> >
{
  static const char* value()
  {
    return "swarm_experiment/SpatialInfo";
  }

  static const char* value(const ::swarm_experiment::SpatialInfo_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::swarm_experiment::SpatialInfo_<ContainerAllocator> >
{
  static const char* value()
  {
    return "# 空间信息消息\n"
"string camera_name      # 摄像头名称\n"
"int32 track_id         # 跟踪ID\n"
"float32 distance       # 距离(米)\n"
"float32 azimuth        # 相对摄像头方位角(度)\n"
"float32 world_azimuth  # 世界坐标系方位角(度)\n"
"float32 position_x     # X位置(米)\n"
"float32 position_y     # Y位置(米)\n"
"float32 confidence     # 置信度\n"
"int32 detection_count  # 检测次数\n"
"float64 timestamp      # 时间戳\n"
;
  }

  static const char* value(const ::swarm_experiment::SpatialInfo_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::swarm_experiment::SpatialInfo_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.camera_name);
      stream.next(m.track_id);
      stream.next(m.distance);
      stream.next(m.azimuth);
      stream.next(m.world_azimuth);
      stream.next(m.position_x);
      stream.next(m.position_y);
      stream.next(m.confidence);
      stream.next(m.detection_count);
      stream.next(m.timestamp);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct SpatialInfo_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::swarm_experiment::SpatialInfo_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::swarm_experiment::SpatialInfo_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "camera_name: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.camera_name);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "track_id: ";
    Printer<int32_t>::stream(s, indent + "  ", v.track_id);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "distance: ";
    Printer<float>::stream(s, indent + "  ", v.distance);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "azimuth: ";
    Printer<float>::stream(s, indent + "  ", v.azimuth);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "world_azimuth: ";
    Printer<float>::stream(s, indent + "  ", v.world_azimuth);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "position_x: ";
    Printer<float>::stream(s, indent + "  ", v.position_x);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "position_y: ";
    Printer<float>::stream(s, indent + "  ", v.position_y);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "confidence: ";
    Printer<float>::stream(s, indent + "  ", v.confidence);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "detection_count: ";
    Printer<int32_t>::stream(s, indent + "  ", v.detection_count);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "timestamp: ";
    Printer<double>::stream(s, indent + "  ", v.timestamp);
  }
};

} // namespace message_operations
} // namespace ros

#endif // SWARM_EXPERIMENT_MESSAGE_SPATIALINFO_H
