// Generated by gencpp from file swarm_experiment/SetLightResponse.msg
// DO NOT EDIT!


#ifndef SWARM_EXPERIMENT_MESSAGE_SETLIGHTRESPONSE_H
#define SWARM_EXPERIMENT_MESSAGE_SETLIGHTRESPONSE_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>


namespace swarm_experiment
{
template <class ContainerAllocator>
struct SetLightResponse_
{
  typedef SetLightResponse_<ContainerAllocator> Type;

  SetLightResponse_()
    : success(false)
    , message()  {
    }
  SetLightResponse_(const ContainerAllocator& _alloc)
    : success(false)
    , message(_alloc)  {
  (void)_alloc;
    }



   typedef uint8_t _success_type;
  _success_type success;

   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _message_type;
  _message_type message;





  typedef boost::shared_ptr< ::swarm_experiment::SetLightResponse_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::swarm_experiment::SetLightResponse_<ContainerAllocator> const> ConstPtr;

}; // struct SetLightResponse_

typedef ::swarm_experiment::SetLightResponse_<std::allocator<void> > SetLightResponse;

typedef boost::shared_ptr< ::swarm_experiment::SetLightResponse > SetLightResponsePtr;
typedef boost::shared_ptr< ::swarm_experiment::SetLightResponse const> SetLightResponseConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::swarm_experiment::SetLightResponse_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::swarm_experiment::SetLightResponse_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::swarm_experiment::SetLightResponse_<ContainerAllocator1> & lhs, const ::swarm_experiment::SetLightResponse_<ContainerAllocator2> & rhs)
{
  return lhs.success == rhs.success &&
    lhs.message == rhs.message;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::swarm_experiment::SetLightResponse_<ContainerAllocator1> & lhs, const ::swarm_experiment::SetLightResponse_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace swarm_experiment

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::swarm_experiment::SetLightResponse_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::swarm_experiment::SetLightResponse_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::swarm_experiment::SetLightResponse_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::swarm_experiment::SetLightResponse_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::swarm_experiment::SetLightResponse_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::swarm_experiment::SetLightResponse_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::swarm_experiment::SetLightResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "937c9679a518e3a18d831e57125ea522";
  }

  static const char* value(const ::swarm_experiment::SetLightResponse_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x937c9679a518e3a1ULL;
  static const uint64_t static_value2 = 0x8d831e57125ea522ULL;
};

template<class ContainerAllocator>
struct DataType< ::swarm_experiment::SetLightResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "swarm_experiment/SetLightResponse";
  }

  static const char* value(const ::swarm_experiment::SetLightResponse_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::swarm_experiment::SetLightResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "bool success            # 是否成功\n"
"string message          # 响应消息\n"
"\n"
;
  }

  static const char* value(const ::swarm_experiment::SetLightResponse_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::swarm_experiment::SetLightResponse_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.success);
      stream.next(m.message);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct SetLightResponse_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::swarm_experiment::SetLightResponse_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::swarm_experiment::SetLightResponse_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "success: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.success);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "message: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.message);
  }
};

} // namespace message_operations
} // namespace ros

#endif // SWARM_EXPERIMENT_MESSAGE_SETLIGHTRESPONSE_H
