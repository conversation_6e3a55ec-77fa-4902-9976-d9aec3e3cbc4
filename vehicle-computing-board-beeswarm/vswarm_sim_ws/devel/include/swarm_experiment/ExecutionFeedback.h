// Generated by gencpp from file swarm_experiment/ExecutionFeedback.msg
// DO NOT EDIT!


#ifndef SWARM_EXPERIMENT_MESSAGE_EXECUTIONFEEDBACK_H
#define SWARM_EXPERIMENT_MESSAGE_EXECUTIONFEEDBACK_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <std_msgs/Header.h>

namespace swarm_experiment
{
template <class ContainerAllocator>
struct ExecutionFeedback_
{
  typedef ExecutionFeedback_<ContainerAllocator> Type;

  ExecutionFeedback_()
    : header()
    , command_name()
    , track_id(0)
    , position_error_x(0.0)
    , position_error_y(0.0)
    , heading_error(0.0)
    , control_output_x(0.0)
    , control_output_y(0.0)
    , control_output_theta(0.0)
    , total_position_error(0.0)
    , is_converged(false)  {
    }
  ExecutionFeedback_(const ContainerAllocator& _alloc)
    : header(_alloc)
    , command_name(_alloc)
    , track_id(0)
    , position_error_x(0.0)
    , position_error_y(0.0)
    , heading_error(0.0)
    , control_output_x(0.0)
    , control_output_y(0.0)
    , control_output_theta(0.0)
    , total_position_error(0.0)
    , is_converged(false)  {
  (void)_alloc;
    }



   typedef  ::std_msgs::Header_<ContainerAllocator>  _header_type;
  _header_type header;

   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _command_name_type;
  _command_name_type command_name;

   typedef int32_t _track_id_type;
  _track_id_type track_id;

   typedef float _position_error_x_type;
  _position_error_x_type position_error_x;

   typedef float _position_error_y_type;
  _position_error_y_type position_error_y;

   typedef float _heading_error_type;
  _heading_error_type heading_error;

   typedef float _control_output_x_type;
  _control_output_x_type control_output_x;

   typedef float _control_output_y_type;
  _control_output_y_type control_output_y;

   typedef float _control_output_theta_type;
  _control_output_theta_type control_output_theta;

   typedef float _total_position_error_type;
  _total_position_error_type total_position_error;

   typedef uint8_t _is_converged_type;
  _is_converged_type is_converged;





  typedef boost::shared_ptr< ::swarm_experiment::ExecutionFeedback_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::swarm_experiment::ExecutionFeedback_<ContainerAllocator> const> ConstPtr;

}; // struct ExecutionFeedback_

typedef ::swarm_experiment::ExecutionFeedback_<std::allocator<void> > ExecutionFeedback;

typedef boost::shared_ptr< ::swarm_experiment::ExecutionFeedback > ExecutionFeedbackPtr;
typedef boost::shared_ptr< ::swarm_experiment::ExecutionFeedback const> ExecutionFeedbackConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::swarm_experiment::ExecutionFeedback_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::swarm_experiment::ExecutionFeedback_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::swarm_experiment::ExecutionFeedback_<ContainerAllocator1> & lhs, const ::swarm_experiment::ExecutionFeedback_<ContainerAllocator2> & rhs)
{
  return lhs.header == rhs.header &&
    lhs.command_name == rhs.command_name &&
    lhs.track_id == rhs.track_id &&
    lhs.position_error_x == rhs.position_error_x &&
    lhs.position_error_y == rhs.position_error_y &&
    lhs.heading_error == rhs.heading_error &&
    lhs.control_output_x == rhs.control_output_x &&
    lhs.control_output_y == rhs.control_output_y &&
    lhs.control_output_theta == rhs.control_output_theta &&
    lhs.total_position_error == rhs.total_position_error &&
    lhs.is_converged == rhs.is_converged;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::swarm_experiment::ExecutionFeedback_<ContainerAllocator1> & lhs, const ::swarm_experiment::ExecutionFeedback_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace swarm_experiment

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::swarm_experiment::ExecutionFeedback_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::swarm_experiment::ExecutionFeedback_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::swarm_experiment::ExecutionFeedback_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::swarm_experiment::ExecutionFeedback_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::swarm_experiment::ExecutionFeedback_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::swarm_experiment::ExecutionFeedback_<ContainerAllocator> const>
  : TrueType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::swarm_experiment::ExecutionFeedback_<ContainerAllocator> >
{
  static const char* value()
  {
    return "907dac725bf7fffbdb15dad8e0617a87";
  }

  static const char* value(const ::swarm_experiment::ExecutionFeedback_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x907dac725bf7fffbULL;
  static const uint64_t static_value2 = 0xdb15dad8e0617a87ULL;
};

template<class ContainerAllocator>
struct DataType< ::swarm_experiment::ExecutionFeedback_<ContainerAllocator> >
{
  static const char* value()
  {
    return "swarm_experiment/ExecutionFeedback";
  }

  static const char* value(const ::swarm_experiment::ExecutionFeedback_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::swarm_experiment::ExecutionFeedback_<ContainerAllocator> >
{
  static const char* value()
  {
    return "# 执行反馈消息\n"
"Header header\n"
"string command_name         # 当前执行的指令名称\n"
"int32 track_id             # 目标跟踪ID\n"
"float32 position_error_x    # X位置误差\n"
"float32 position_error_y    # Y位置误差\n"
"float32 heading_error       # 朝向误差\n"
"float32 control_output_x    # X控制输出\n"
"float32 control_output_y    # Y控制输出\n"
"float32 control_output_theta # 角度控制输出\n"
"float32 total_position_error # 总位置误差\n"
"bool is_converged          # 是否收敛\n"
"\n"
"================================================================================\n"
"MSG: std_msgs/Header\n"
"# Standard metadata for higher-level stamped data types.\n"
"# This is generally used to communicate timestamped data \n"
"# in a particular coordinate frame.\n"
"# \n"
"# sequence ID: consecutively increasing ID \n"
"uint32 seq\n"
"#Two-integer timestamp that is expressed as:\n"
"# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')\n"
"# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')\n"
"# time-handling sugar is provided by the client library\n"
"time stamp\n"
"#Frame this data is associated with\n"
"string frame_id\n"
;
  }

  static const char* value(const ::swarm_experiment::ExecutionFeedback_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::swarm_experiment::ExecutionFeedback_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.header);
      stream.next(m.command_name);
      stream.next(m.track_id);
      stream.next(m.position_error_x);
      stream.next(m.position_error_y);
      stream.next(m.heading_error);
      stream.next(m.control_output_x);
      stream.next(m.control_output_y);
      stream.next(m.control_output_theta);
      stream.next(m.total_position_error);
      stream.next(m.is_converged);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct ExecutionFeedback_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::swarm_experiment::ExecutionFeedback_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::swarm_experiment::ExecutionFeedback_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "header: ";
    Printer< ::std_msgs::Header_<ContainerAllocator> >::stream(s, indent + "  ", v.header);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "command_name: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.command_name);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "track_id: ";
    Printer<int32_t>::stream(s, indent + "  ", v.track_id);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "position_error_x: ";
    Printer<float>::stream(s, indent + "  ", v.position_error_x);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "position_error_y: ";
    Printer<float>::stream(s, indent + "  ", v.position_error_y);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "heading_error: ";
    Printer<float>::stream(s, indent + "  ", v.heading_error);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "control_output_x: ";
    Printer<float>::stream(s, indent + "  ", v.control_output_x);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "control_output_y: ";
    Printer<float>::stream(s, indent + "  ", v.control_output_y);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "control_output_theta: ";
    Printer<float>::stream(s, indent + "  ", v.control_output_theta);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "total_position_error: ";
    Printer<float>::stream(s, indent + "  ", v.total_position_error);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "is_converged: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.is_converged);
  }
};

} // namespace message_operations
} // namespace ros

#endif // SWARM_EXPERIMENT_MESSAGE_EXECUTIONFEEDBACK_H
