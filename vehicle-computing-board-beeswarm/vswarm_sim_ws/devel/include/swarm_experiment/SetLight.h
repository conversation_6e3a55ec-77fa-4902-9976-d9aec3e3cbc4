// Generated by gencpp from file swarm_experiment/SetLight.msg
// DO NOT EDIT!


#ifndef SWARM_EXPERIMENT_MESSAGE_SETLIGHT_H
#define SWARM_EXPERIMENT_MESSAGE_SETLIGHT_H

#include <ros/service_traits.h>


#include <swarm_experiment/SetLightRequest.h>
#include <swarm_experiment/SetLightResponse.h>


namespace swarm_experiment
{

struct SetLight
{

typedef SetLightRequest Request;
typedef SetLightResponse Response;
Request request;
Response response;

typedef Request RequestType;
typedef Response ResponseType;

}; // struct SetLight
} // namespace swarm_experiment


namespace ros
{
namespace service_traits
{


template<>
struct MD5Sum< ::swarm_experiment::SetLight > {
  static const char* value()
  {
    return "e411def5f68acd335fe0434e90d7d05b";
  }

  static const char* value(const ::swarm_experiment::SetLight&) { return value(); }
};

template<>
struct DataType< ::swarm_experiment::SetLight > {
  static const char* value()
  {
    return "swarm_experiment/SetLight";
  }

  static const char* value(const ::swarm_experiment::SetLight&) { return value(); }
};


// service_traits::MD5Sum< ::swarm_experiment::SetLightRequest> should match
// service_traits::MD5Sum< ::swarm_experiment::SetLight >
template<>
struct MD5Sum< ::swarm_experiment::SetLightRequest>
{
  static const char* value()
  {
    return MD5Sum< ::swarm_experiment::SetLight >::value();
  }
  static const char* value(const ::swarm_experiment::SetLightRequest&)
  {
    return value();
  }
};

// service_traits::DataType< ::swarm_experiment::SetLightRequest> should match
// service_traits::DataType< ::swarm_experiment::SetLight >
template<>
struct DataType< ::swarm_experiment::SetLightRequest>
{
  static const char* value()
  {
    return DataType< ::swarm_experiment::SetLight >::value();
  }
  static const char* value(const ::swarm_experiment::SetLightRequest&)
  {
    return value();
  }
};

// service_traits::MD5Sum< ::swarm_experiment::SetLightResponse> should match
// service_traits::MD5Sum< ::swarm_experiment::SetLight >
template<>
struct MD5Sum< ::swarm_experiment::SetLightResponse>
{
  static const char* value()
  {
    return MD5Sum< ::swarm_experiment::SetLight >::value();
  }
  static const char* value(const ::swarm_experiment::SetLightResponse&)
  {
    return value();
  }
};

// service_traits::DataType< ::swarm_experiment::SetLightResponse> should match
// service_traits::DataType< ::swarm_experiment::SetLight >
template<>
struct DataType< ::swarm_experiment::SetLightResponse>
{
  static const char* value()
  {
    return DataType< ::swarm_experiment::SetLight >::value();
  }
  static const char* value(const ::swarm_experiment::SetLightResponse&)
  {
    return value();
  }
};

} // namespace service_traits
} // namespace ros

#endif // SWARM_EXPERIMENT_MESSAGE_SETLIGHT_H
