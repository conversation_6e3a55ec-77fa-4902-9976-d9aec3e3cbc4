// Generated by gencpp from file swarm_experiment/PredictedState.msg
// DO NOT EDIT!


#ifndef SWARM_EXPERIMENT_MESSAGE_PREDICTEDSTATE_H
#define SWARM_EXPERIMENT_MESSAGE_PREDICTEDSTATE_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>


namespace swarm_experiment
{
template <class ContainerAllocator>
struct PredictedState_
{
  typedef PredictedState_<ContainerAllocator> Type;

  PredictedState_()
    : entity_type()
    , entity_id()
    , predicted_time(0.0)
    , position_x(0.0)
    , position_y(0.0)
    , velocity_x(0.0)
    , velocity_y(0.0)
    , heading(0.0)
    , uncertainty_radius(0.0)
    , confidence(0.0)  {
    }
  PredictedState_(const ContainerAllocator& _alloc)
    : entity_type(_alloc)
    , entity_id(_alloc)
    , predicted_time(0.0)
    , position_x(0.0)
    , position_y(0.0)
    , velocity_x(0.0)
    , velocity_y(0.0)
    , heading(0.0)
    , uncertainty_radius(0.0)
    , confidence(0.0)  {
  (void)_alloc;
    }



   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _entity_type_type;
  _entity_type_type entity_type;

   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _entity_id_type;
  _entity_id_type entity_id;

   typedef double _predicted_time_type;
  _predicted_time_type predicted_time;

   typedef float _position_x_type;
  _position_x_type position_x;

   typedef float _position_y_type;
  _position_y_type position_y;

   typedef float _velocity_x_type;
  _velocity_x_type velocity_x;

   typedef float _velocity_y_type;
  _velocity_y_type velocity_y;

   typedef float _heading_type;
  _heading_type heading;

   typedef float _uncertainty_radius_type;
  _uncertainty_radius_type uncertainty_radius;

   typedef float _confidence_type;
  _confidence_type confidence;





  typedef boost::shared_ptr< ::swarm_experiment::PredictedState_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::swarm_experiment::PredictedState_<ContainerAllocator> const> ConstPtr;

}; // struct PredictedState_

typedef ::swarm_experiment::PredictedState_<std::allocator<void> > PredictedState;

typedef boost::shared_ptr< ::swarm_experiment::PredictedState > PredictedStatePtr;
typedef boost::shared_ptr< ::swarm_experiment::PredictedState const> PredictedStateConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::swarm_experiment::PredictedState_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::swarm_experiment::PredictedState_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::swarm_experiment::PredictedState_<ContainerAllocator1> & lhs, const ::swarm_experiment::PredictedState_<ContainerAllocator2> & rhs)
{
  return lhs.entity_type == rhs.entity_type &&
    lhs.entity_id == rhs.entity_id &&
    lhs.predicted_time == rhs.predicted_time &&
    lhs.position_x == rhs.position_x &&
    lhs.position_y == rhs.position_y &&
    lhs.velocity_x == rhs.velocity_x &&
    lhs.velocity_y == rhs.velocity_y &&
    lhs.heading == rhs.heading &&
    lhs.uncertainty_radius == rhs.uncertainty_radius &&
    lhs.confidence == rhs.confidence;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::swarm_experiment::PredictedState_<ContainerAllocator1> & lhs, const ::swarm_experiment::PredictedState_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace swarm_experiment

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::swarm_experiment::PredictedState_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::swarm_experiment::PredictedState_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::swarm_experiment::PredictedState_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::swarm_experiment::PredictedState_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::swarm_experiment::PredictedState_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::swarm_experiment::PredictedState_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::swarm_experiment::PredictedState_<ContainerAllocator> >
{
  static const char* value()
  {
    return "541b4cef9d15fdef1cac62835fb2a769";
  }

  static const char* value(const ::swarm_experiment::PredictedState_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x541b4cef9d15fdefULL;
  static const uint64_t static_value2 = 0x1cac62835fb2a769ULL;
};

template<class ContainerAllocator>
struct DataType< ::swarm_experiment::PredictedState_<ContainerAllocator> >
{
  static const char* value()
  {
    return "swarm_experiment/PredictedState";
  }

  static const char* value(const ::swarm_experiment::PredictedState_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::swarm_experiment::PredictedState_<ContainerAllocator> >
{
  static const char* value()
  {
    return "# 预测状态消息\n"
"string entity_type      # 实体类型 (\"target\", \"self\", \"obstacle\")\n"
"string entity_id        # 实体ID\n"
"float64 predicted_time  # 预测时间戳\n"
"float32 position_x      # 预测X位置\n"
"float32 position_y      # 预测Y位置\n"
"float32 velocity_x      # 预测X速度\n"
"float32 velocity_y      # 预测Y速度\n"
"float32 heading         # 预测朝向(弧度)\n"
"float32 uncertainty_radius  # 不确定性半径\n"
"float32 confidence      # 预测置信度\n"
;
  }

  static const char* value(const ::swarm_experiment::PredictedState_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::swarm_experiment::PredictedState_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.entity_type);
      stream.next(m.entity_id);
      stream.next(m.predicted_time);
      stream.next(m.position_x);
      stream.next(m.position_y);
      stream.next(m.velocity_x);
      stream.next(m.velocity_y);
      stream.next(m.heading);
      stream.next(m.uncertainty_radius);
      stream.next(m.confidence);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct PredictedState_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::swarm_experiment::PredictedState_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::swarm_experiment::PredictedState_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "entity_type: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.entity_type);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "entity_id: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.entity_id);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "predicted_time: ";
    Printer<double>::stream(s, indent + "  ", v.predicted_time);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "position_x: ";
    Printer<float>::stream(s, indent + "  ", v.position_x);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "position_y: ";
    Printer<float>::stream(s, indent + "  ", v.position_y);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "velocity_x: ";
    Printer<float>::stream(s, indent + "  ", v.velocity_x);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "velocity_y: ";
    Printer<float>::stream(s, indent + "  ", v.velocity_y);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "heading: ";
    Printer<float>::stream(s, indent + "  ", v.heading);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "uncertainty_radius: ";
    Printer<float>::stream(s, indent + "  ", v.uncertainty_radius);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "confidence: ";
    Printer<float>::stream(s, indent + "  ", v.confidence);
  }
};

} // namespace message_operations
} // namespace ros

#endif // SWARM_EXPERIMENT_MESSAGE_PREDICTEDSTATE_H
