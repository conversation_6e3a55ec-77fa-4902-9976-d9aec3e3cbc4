// Generated by gencpp from file swarm_experiment/SetLightRequest.msg
// DO NOT EDIT!


#ifndef SWARM_EXPERIMENT_MESSAGE_SETLIGHTREQUEST_H
#define SWARM_EXPERIMENT_MESSAGE_SETLIGHTREQUEST_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>


namespace swarm_experiment
{
template <class ContainerAllocator>
struct SetLightRequest_
{
  typedef SetLightRequest_<ContainerAllocator> Type;

  SetLightRequest_()
    : light_semantic()
    , duration(0.0)  {
    }
  SetLightRequest_(const ContainerAllocator& _alloc)
    : light_semantic(_alloc)
    , duration(0.0)  {
  (void)_alloc;
    }



   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _light_semantic_type;
  _light_semantic_type light_semantic;

   typedef float _duration_type;
  _duration_type duration;





  typedef boost::shared_ptr< ::swarm_experiment::SetLightRequest_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::swarm_experiment::SetLightRequest_<ContainerAllocator> const> ConstPtr;

}; // struct SetLightRequest_

typedef ::swarm_experiment::SetLightRequest_<std::allocator<void> > SetLightRequest;

typedef boost::shared_ptr< ::swarm_experiment::SetLightRequest > SetLightRequestPtr;
typedef boost::shared_ptr< ::swarm_experiment::SetLightRequest const> SetLightRequestConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::swarm_experiment::SetLightRequest_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::swarm_experiment::SetLightRequest_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::swarm_experiment::SetLightRequest_<ContainerAllocator1> & lhs, const ::swarm_experiment::SetLightRequest_<ContainerAllocator2> & rhs)
{
  return lhs.light_semantic == rhs.light_semantic &&
    lhs.duration == rhs.duration;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::swarm_experiment::SetLightRequest_<ContainerAllocator1> & lhs, const ::swarm_experiment::SetLightRequest_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace swarm_experiment

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::swarm_experiment::SetLightRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::swarm_experiment::SetLightRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::swarm_experiment::SetLightRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::swarm_experiment::SetLightRequest_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::swarm_experiment::SetLightRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::swarm_experiment::SetLightRequest_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::swarm_experiment::SetLightRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "7faeb0fe9384a19ece3606dc061e0ae8";
  }

  static const char* value(const ::swarm_experiment::SetLightRequest_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x7faeb0fe9384a19eULL;
  static const uint64_t static_value2 = 0xce3606dc061e0ae8ULL;
};

template<class ContainerAllocator>
struct DataType< ::swarm_experiment::SetLightRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "swarm_experiment/SetLightRequest";
  }

  static const char* value(const ::swarm_experiment::SetLightRequest_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::swarm_experiment::SetLightRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "# 设置灯语服务定义\n"
"string light_semantic    # 灯语语义 (launch, gather, follow, disperse, finish, stop, emergency)\n"
"float32 duration         # 持续时间(秒)，0表示持续到下次切换\n"
;
  }

  static const char* value(const ::swarm_experiment::SetLightRequest_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::swarm_experiment::SetLightRequest_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.light_semantic);
      stream.next(m.duration);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct SetLightRequest_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::swarm_experiment::SetLightRequest_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::swarm_experiment::SetLightRequest_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "light_semantic: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.light_semantic);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "duration: ";
    Printer<float>::stream(s, indent + "  ", v.duration);
  }
};

} // namespace message_operations
} // namespace ros

#endif // SWARM_EXPERIMENT_MESSAGE_SETLIGHTREQUEST_H
