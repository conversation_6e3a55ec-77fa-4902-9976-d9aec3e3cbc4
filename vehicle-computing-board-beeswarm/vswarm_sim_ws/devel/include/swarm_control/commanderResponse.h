// Generated by gencpp from file swarm_control/commanderResponse.msg
// DO NOT EDIT!


#ifndef SWARM_CONTROL_MESSAGE_COMMANDERRESPONSE_H
#define SWARM_CONTROL_MESSAGE_COMMANDERRESPONSE_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>


namespace swarm_control
{
template <class ContainerAllocator>
struct commanderResponse_
{
  typedef commanderResponse_<ContainerAllocator> Type;

  commanderResponse_()
    : success(false)  {
    }
  commanderResponse_(const ContainerAllocator& _alloc)
    : success(false)  {
  (void)_alloc;
    }



   typedef uint8_t _success_type;
  _success_type success;





  typedef boost::shared_ptr< ::swarm_control::commanderResponse_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::swarm_control::commander<PERSON><PERSON>ponse_<ContainerAllocator> const> ConstPtr;

}; // struct commanderResponse_

typedef ::swarm_control::commanderResponse_<std::allocator<void> > commanderResponse;

typedef boost::shared_ptr< ::swarm_control::commanderResponse > commanderResponsePtr;
typedef boost::shared_ptr< ::swarm_control::commanderResponse const> commanderResponseConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::swarm_control::commanderResponse_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::swarm_control::commanderResponse_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::swarm_control::commanderResponse_<ContainerAllocator1> & lhs, const ::swarm_control::commanderResponse_<ContainerAllocator2> & rhs)
{
  return lhs.success == rhs.success;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::swarm_control::commanderResponse_<ContainerAllocator1> & lhs, const ::swarm_control::commanderResponse_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace swarm_control

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::swarm_control::commanderResponse_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::swarm_control::commanderResponse_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::swarm_control::commanderResponse_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::swarm_control::commanderResponse_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::swarm_control::commanderResponse_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::swarm_control::commanderResponse_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::swarm_control::commanderResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "358e233cde0c8a8bcfea4ce193f8fc15";
  }

  static const char* value(const ::swarm_control::commanderResponse_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x358e233cde0c8a8bULL;
  static const uint64_t static_value2 = 0xcfea4ce193f8fc15ULL;
};

template<class ContainerAllocator>
struct DataType< ::swarm_control::commanderResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "swarm_control/commanderResponse";
  }

  static const char* value(const ::swarm_control::commanderResponse_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::swarm_control::commanderResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "bool success  # 成功执行命令后返回true \n"
;
  }

  static const char* value(const ::swarm_control::commanderResponse_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::swarm_control::commanderResponse_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.success);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct commanderResponse_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::swarm_control::commanderResponse_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::swarm_control::commanderResponse_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "success: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.success);
  }
};

} // namespace message_operations
} // namespace ros

#endif // SWARM_CONTROL_MESSAGE_COMMANDERRESPONSE_H
