// Generated by gencpp from file swarm_control/commanderRequest.msg
// DO NOT EDIT!


#ifndef SWARM_CONTROL_MESSAGE_COMMANDERREQUEST_H
#define SWARM_CONTROL_MESSAGE_COMMANDERREQUEST_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>


namespace swarm_control
{
template <class ContainerAllocator>
struct commanderRequest_
{
  typedef commanderRequest_<ContainerAllocator> Type;

  commanderRequest_()
    : command()  {
    }
  commanderRequest_(const ContainerAllocator& _alloc)
    : command(_alloc)  {
  (void)_alloc;
    }



   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _command_type;
  _command_type command;





  typedef boost::shared_ptr< ::swarm_control::commanderRequest_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::swarm_control::commanderRequest_<ContainerAllocator> const> ConstPtr;

}; // struct commanderRequest_

typedef ::swarm_control::commanderRequest_<std::allocator<void> > commanderRequest;

typedef boost::shared_ptr< ::swarm_control::commanderRequest > commanderRequestPtr;
typedef boost::shared_ptr< ::swarm_control::commanderRequest const> commanderRequestConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::swarm_control::commanderRequest_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::swarm_control::commanderRequest_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::swarm_control::commanderRequest_<ContainerAllocator1> & lhs, const ::swarm_control::commanderRequest_<ContainerAllocator2> & rhs)
{
  return lhs.command == rhs.command;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::swarm_control::commanderRequest_<ContainerAllocator1> & lhs, const ::swarm_control::commanderRequest_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace swarm_control

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::swarm_control::commanderRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::swarm_control::commanderRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::swarm_control::commanderRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::swarm_control::commanderRequest_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::swarm_control::commanderRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::swarm_control::commanderRequest_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::swarm_control::commanderRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "cba5e21e920a3a2b7b375cb65b64cdea";
  }

  static const char* value(const ::swarm_control::commanderRequest_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0xcba5e21e920a3a2bULL;
  static const uint64_t static_value2 = 0x7b375cb65b64cdeaULL;
};

template<class ContainerAllocator>
struct DataType< ::swarm_control::commanderRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "swarm_control/commanderRequest";
  }

  static const char* value(const ::swarm_control::commanderRequest_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::swarm_control::commanderRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "string command  # \"AXX\"格式的命令，其中XX是两位数字\n"
;
  }

  static const char* value(const ::swarm_control::commanderRequest_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::swarm_control::commanderRequest_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.command);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct commanderRequest_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::swarm_control::commanderRequest_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::swarm_control::commanderRequest_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "command: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.command);
  }
};

} // namespace message_operations
} // namespace ros

#endif // SWARM_CONTROL_MESSAGE_COMMANDERREQUEST_H
