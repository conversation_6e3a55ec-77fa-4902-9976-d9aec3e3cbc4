// Generated by gencpp from file swarm_control/commander.msg
// DO NOT EDIT!


#ifndef SWARM_CONTROL_MESSAGE_COMMANDER_H
#define SWARM_CONTROL_MESSAGE_COMMANDER_H

#include <ros/service_traits.h>


#include <swarm_control/commanderRequest.h>
#include <swarm_control/commanderResponse.h>


namespace swarm_control
{

struct commander
{

typedef commanderRequest Request;
typedef commanderResponse Response;
Request request;
Response response;

typedef Request RequestType;
typedef Response ResponseType;

}; // struct commander
} // namespace swarm_control


namespace ros
{
namespace service_traits
{


template<>
struct MD5Sum< ::swarm_control::commander > {
  static const char* value()
  {
    return "031d24522d462b2314fd1b6270670dd2";
  }

  static const char* value(const ::swarm_control::commander&) { return value(); }
};

template<>
struct DataType< ::swarm_control::commander > {
  static const char* value()
  {
    return "swarm_control/commander";
  }

  static const char* value(const ::swarm_control::commander&) { return value(); }
};


// service_traits::MD5Sum< ::swarm_control::commanderRequest> should match
// service_traits::MD5Sum< ::swarm_control::commander >
template<>
struct MD5Sum< ::swarm_control::commanderRequest>
{
  static const char* value()
  {
    return MD5Sum< ::swarm_control::commander >::value();
  }
  static const char* value(const ::swarm_control::commanderRequest&)
  {
    return value();
  }
};

// service_traits::DataType< ::swarm_control::commanderRequest> should match
// service_traits::DataType< ::swarm_control::commander >
template<>
struct DataType< ::swarm_control::commanderRequest>
{
  static const char* value()
  {
    return DataType< ::swarm_control::commander >::value();
  }
  static const char* value(const ::swarm_control::commanderRequest&)
  {
    return value();
  }
};

// service_traits::MD5Sum< ::swarm_control::commanderResponse> should match
// service_traits::MD5Sum< ::swarm_control::commander >
template<>
struct MD5Sum< ::swarm_control::commanderResponse>
{
  static const char* value()
  {
    return MD5Sum< ::swarm_control::commander >::value();
  }
  static const char* value(const ::swarm_control::commanderResponse&)
  {
    return value();
  }
};

// service_traits::DataType< ::swarm_control::commanderResponse> should match
// service_traits::DataType< ::swarm_control::commander >
template<>
struct DataType< ::swarm_control::commanderResponse>
{
  static const char* value()
  {
    return DataType< ::swarm_control::commander >::value();
  }
  static const char* value(const ::swarm_control::commanderResponse&)
  {
    return value();
  }
};

} // namespace service_traits
} // namespace ros

#endif // SWARM_CONTROL_MESSAGE_COMMANDER_H
