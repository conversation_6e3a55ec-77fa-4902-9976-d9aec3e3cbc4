#!/usr/bin/env python3

import rospy
import os
import socket
import time
from std_msgs.msg import Float32MultiArray, Int32

# 设置LED灯的颜色
def LedSetColor(color_list, color):
    color_list.clear()
    color_list.append(((color >> 16) & 0xff) / 256)  # 红色分量
    color_list.append(((color >> 8) & 0xff) / 256)   # 绿色分量
    color_list.append(((color) & 0xff) / 256)        # 蓝色分量
    color_list.append(1)                             # 透明度

# 管理LED状态的类
class LEDController:
    def __init__(self, colors, counts, color_list, publisher, led_name, max_cycles):
        self.colors = colors                  # 颜色列表
        self.counts = counts                  # 每种颜色的显示次数
        self.current_color_idx = 0            # 当前颜色的索引
        self.current_count = 0                # 当前颜色已显示的次数
        self.total_colors = len(colors)       # 总颜色数
        self.color_list = color_list          # 存放当前颜色的列表
        self.publisher = publisher            # 发布者
        self.led_name = led_name              # LED 的名称
        self.cycle_count = 0                  # 计数循环
        self.max_cycles = max_cycles          # 最大循环次数

    def update_color(self):
        # 更新当前颜色的显示次数
        self.current_count += 1

        # 如果达到计数上限，切换到下一种颜色
        if self.current_count >= self.counts[self.current_color_idx]:
            self.current_color_idx = (self.current_color_idx + 1) % len(self.colors)
            self.current_count = 0

        # 设置当前颜色
        LedSetColor(self.color_list, self.colors[self.current_color_idx])

    def publish(self):
        # 发布当前颜色
        pub_msg = Float32MultiArray(data=self.color_list)
        self.publisher.publish(pub_msg)

    def is_cycle_complete(self):
        """判断是否完成了一个完整的颜色循环"""
        return self.current_color_idx == 0 and self.current_count == 0

    def reset(self):
        """重置循环计数"""
        self.current_color_idx = 0
        self.current_count = 0
        self.cycle_count = 0

# 全局变量
led_mode = 0
mode_changed = False
other_nodes_led_modes = {}  # 存储其他节点的LED状态

def led_mode_callback(msg):
    global led_mode, mode_changed
    led_mode = msg.data
    mode_changed = True
    rospy.loginfo(f"Local LED mode changed to: {led_mode}")

def other_node_led_mode_callback(msg, node_name):
    '''其他节点LED模式回调'''
    global other_nodes_led_modes
    old_mode = other_nodes_led_modes.get(node_name, 0)
    new_mode = msg.data
    other_nodes_led_modes[node_name] = new_mode

    if old_mode != new_mode:
        rospy.loginfo(f"🔄 Received {node_name} LED mode change: {old_mode} -> {new_mode}")

    # 标记需要更新其他节点的LED控制器
    global other_node_mode_changed
    if not hasattr(other_node_led_mode_callback, 'changed_nodes'):
        other_node_led_mode_callback.changed_nodes = set()
    other_node_led_mode_callback.changed_nodes.add(node_name)

def create_other_node_publishers(node_name, other_node_publishers):
    '''为其他节点创建LED控制发布者'''
    if node_name not in other_node_publishers:
        other_node_publishers[node_name] = {
            'upper': rospy.Publisher(f"{node_name}/upper_led_ctrl", Float32MultiArray, queue_size=10),
            'lower': rospy.Publisher(f"{node_name}/lower_led_ctrl", Float32MultiArray, queue_size=10)
        }
        rospy.loginfo(f"Created LED publishers for {node_name}")
        time.sleep(0.1)  # 等待发布者初始化

def create_other_node_led_controller(node_name, led_mode, other_node_publishers, other_node_controllers, modes, max_cycles):
    '''为其他节点创建LED控制器'''
    if led_mode in modes:
        color, freq = modes[led_mode]

        # 为其他节点创建发布者
        if node_name not in other_node_publishers:
            other_node_publishers[node_name] = {
                'upper': rospy.Publisher(f"/{node_name}/upper_led_ctrl", Float32MultiArray, queue_size=10),
                'lower': rospy.Publisher(f"/{node_name}/lower_led_ctrl", Float32MultiArray, queue_size=10)
            }
            rospy.loginfo(f"Created LED publishers for {node_name}")
            time.sleep(0.1)  # 等待发布者初始化

        # 为其他节点创建LED控制器
        color_upper = []
        color_lower = []

        other_node_controllers[node_name] = {
            'upper': LEDController([color, 0xCCCCFF], [2, freq], color_upper,
                                 other_node_publishers[node_name]['upper'], f"{node_name}_UPPER", max_cycles),
            'lower': LEDController([color, 0xCCCCFF], [2, freq], color_lower,
                                 other_node_publishers[node_name]['lower'], f"{node_name}_LOWER", max_cycles)
        }

        rospy.loginfo(f"Created LED controllers for {node_name} mode {led_mode} (#{color:06X}, freq: {freq})")

if __name__ == "__main__":
    # 初始化ROS节点
    rospy.init_node("distributed_car_led_ctrl")

    # 获取客户端ID（类似无人机的实现）
    client_id = f'{socket.gethostname()}_car'

    # Gazebo中的模型名称（不包含_car后缀）
    model_name = socket.gethostname()

    # 创建分布式发布者对象 - 发布到Gazebo插件期望的话题名称
    # Gazebo LED插件期望的话题格式: {model_name}/upper_led_ctrl
    pub_upper = rospy.Publisher(f"{model_name}/upper_led_ctrl", Float32MultiArray, queue_size=10)
    pub_lower = rospy.Publisher(f"{model_name}/lower_led_ctrl", Float32MultiArray, queue_size=10)

    # 创建订阅者对象，订阅本地节点特定的LED模式话题
    local_led_topic = f"/{model_name}/led_mode"
    rospy.Subscriber(local_led_topic, Int32, led_mode_callback)
    rospy.loginfo(f"Subscribed to local LED topic: {local_led_topic}")

    # 订阅其他节点的LED模式话题（从CarSim.py发布）
    other_node_names = ['VSWARM11', 'VSWARM15', 'VCAR01', 'VCAR02']
    for node in other_node_names:
        if node != model_name:  # 不订阅自己的话题
            topic_name = f"/other_node_led_mode/{node}"
            rospy.Subscriber(topic_name, Int32,
                           lambda msg, n=node: other_node_led_mode_callback(msg, n))
            rospy.loginfo(f"Subscribed to {topic_name}")

    # 为其他节点创建LED控制发布者字典
    other_node_publishers = {}
    other_node_controllers = {}  # 为其他节点创建LED控制器
    other_node_modes = {}        # 存储其他节点的LED模式

    # 等待一段时间让系统稳定
    time.sleep(2)

    # 启动信息日志
    rospy.loginfo(f"=== Distributed Car LED Controller Started ===")
    rospy.loginfo(f"Client ID: {client_id}")
    rospy.loginfo(f"Model Name: {model_name}")
    rospy.loginfo(f"Publishing to: {model_name}/upper_led_ctrl, {model_name}/lower_led_ctrl")
    rospy.loginfo(f"Subscribing to local: /{model_name}/led_mode")
    rospy.loginfo(f"Subscribing to remote nodes for display purposes")

    # 初始化颜色列表
    color_upper = []
    color_lower = []

    # 定义10种LED模式，与无人机系统保持一致
    # 格式：(颜色, 闪烁频率) - 频率越高闪烁越快
    modes = {
        0: (0xCCCCFF, 1),   # 模式0：常值状态（淡蓝色）
        1: (0xFF0000, 1),   # 模式1：红色，慢闪烁
        2: (0x00FF00, 1),   # 模式2：绿色，慢闪烁
        3: (0x0000FF, 1),   # 模式3：蓝色，慢闪烁
        4: (0xFFFF00, 1),   # 模式4：黄色，慢闪烁
        5: (0xFF00FF, 1),   # 模式5：紫色，慢闪烁
        6: (0xFF0000, 2),   # 模式6：红色，快闪烁
        7: (0x00FF00, 2),   # 模式7：绿色，快闪烁
        8: (0x0000FF, 2),   # 模式8：蓝色，快闪烁
        9: (0xFFFF00, 2),   # 模式9：黄色，快闪烁
        10: (0xFF00FF, 2),  # 模式10：紫色，快闪烁
    }

    # 初始化选中的模式，默认为0
    selected_mode = 0
    mode_changed = False

    # 设置最大循环次数
    max_cycles = 10

    # 从模式字典中获取颜色和时间
    a, b = modes[selected_mode]

    # 显示选择的模式对应的颜色和时间
    rospy.loginfo(f"Vehicle {client_id} - Selected Mode: {selected_mode}, Color: #{a:06X}, Time: {b}")
    
    # 配置LED控制器 - 上下两个LED
    led_upper_controller = LEDController([a, 0xCCCCFF], [2, b], color_upper, pub_upper, "LED_UPPER", max_cycles)
    led_lower_controller = LEDController([a, 0xCCCCFF], [2, b], color_lower, pub_lower, "LED_LOWER", max_cycles)
    
    tick = 0

    # 设置循环频率为100Hz（与无人机保持一致，实现精确的LED控制）
    rate = rospy.Rate(100)
    
    # 进入循环，直到ROS节点关闭
    while not rospy.is_shutdown():
        tick += 1

        # 如果接收到新模式，则重新配置LED控制器
        if mode_changed:
            old_mode = selected_mode
            selected_mode = led_mode  # 更新选中的模式为接收到的LED模式
            if selected_mode in modes:
                a, b = modes[selected_mode]
                led_upper_controller = LEDController([a, 0xCCCCFF], [2, b], color_upper, pub_upper, "LED_UPPER", max_cycles)
                led_lower_controller = LEDController([a, 0xCCCCFF], [2, b], color_lower, pub_lower, "LED_LOWER", max_cycles)
                mode_changed = False  # 重置标记
                rospy.loginfo(f"🚗 Vehicle {client_id} LED mode changed: {old_mode} -> {selected_mode} (Color: #{a:06X}, Freq: {b})")
            else:
                rospy.logwarn(f"⚠️ Vehicle {client_id} received invalid LED mode: {led_mode}")
                mode_changed = False  # 重置标记以避免无限循环

        # 处理其他节点的LED模式变化
        if hasattr(other_node_led_mode_callback, 'changed_nodes') and other_node_led_mode_callback.changed_nodes:
            for node_name in list(other_node_led_mode_callback.changed_nodes):
                if node_name in other_nodes_led_modes:
                    node_led_mode = other_nodes_led_modes[node_name]
                    create_other_node_led_controller(node_name, node_led_mode, other_node_publishers,
                                                   other_node_controllers, modes, max_cycles)
                    other_node_modes[node_name] = node_led_mode
            other_node_led_mode_callback.changed_nodes.clear()

        # 根据模式动态调整LED更新频率
        # 模式0: 常亮，不闪烁
        # 模式1-5,11,12,15: 慢闪烁 (每20个tick，相当于5Hz)
        # 模式6-10,13: 快闪烁 (每10个tick，相当于10Hz)
        # 模式14: 超快闪烁 (每5个tick，相当于20Hz)

        update_interval = 20  # 默认慢闪烁
        if selected_mode == 0:
            update_interval = 100  # 常亮模式，很少更新
        elif selected_mode in [6, 7, 8, 9, 10, 13]:
            update_interval = 10   # 快闪烁
        elif selected_mode == 14:
            update_interval = 5    # 超快闪烁

        if tick % update_interval == 0:
            # 更新并发布本节点的上下LED颜色
            led_upper_controller.update_color()
            led_upper_controller.publish()

            led_lower_controller.update_color()
            led_lower_controller.publish()

            # 检查是否完成了一个完整的颜色循环
            if led_upper_controller.is_cycle_complete():
                led_upper_controller.cycle_count += 1
            if led_lower_controller.is_cycle_complete():
                led_lower_controller.cycle_count += 1

            # 如果循环次数达到最大值，则固定颜色为0xCCCCFF，并重新初始化LED控制器为模式0
            if led_upper_controller.cycle_count >= max_cycles:
                old_selected_mode = selected_mode
                selected_mode = 0  # 切换到模式0
                led_upper_controller = LEDController([0xCCCCFF], [1], color_upper, pub_upper, "LED_UPPER", max_cycles)
                led_lower_controller = LEDController([0xCCCCFF], [1], color_lower, pub_lower, "LED_LOWER", max_cycles)
                led_upper_controller.publish()
                led_lower_controller.publish()

                # 🔧 修复：通知本地节点LED状态重置
                if old_selected_mode != 0:
                    rospy.loginfo(f"🔄 Vehicle {client_id} LED auto-reset: {old_selected_mode} -> 0 (max cycles reached)")
                    # 发布LED模式重置消息到本地节点特定话题，让CarSim.py同步状态
                    reset_topic = f"/{model_name}/led_mode"
                    reset_pub = rospy.Publisher(reset_topic, Int32, queue_size=10, latch=True)
                    rospy.sleep(0.1)  # 等待发布者初始化
                    reset_msg = Int32()
                    reset_msg.data = 0
                    reset_pub.publish(reset_msg)
                    rospy.loginfo(f"📡 Published LED reset notification to {reset_topic}: mode 0")

            # 更新其他节点的LED控制器（使用与本节点相同的更新间隔以保持同步）
            for node_name, controllers in other_node_controllers.items():
                if node_name in other_node_modes:
                    node_mode = other_node_modes[node_name]

                    # 使用与本节点相同的更新间隔，确保频率同步
                    # 这样可以避免频率差异和时延问题
                    controllers['upper'].update_color()
                    controllers['upper'].publish()
                    controllers['lower'].update_color()
                    controllers['lower'].publish()

        # 等待下一次循环
        rate.sleep()

    # 🔧 修复：节点退出时发布LED状态重置
    try:
        rospy.loginfo(f"🔄 Vehicle {client_id} shutting down, resetting LED to mode 0")
        # 发布LED重置消息
        reset_pub = rospy.Publisher('/led_mode', Int32, queue_size=10, latch=True)
        rospy.sleep(0.1)
        reset_msg = Int32()
        reset_msg.data = 0
        reset_pub.publish(reset_msg)
        rospy.loginfo(f"📡 Published shutdown LED reset: mode 0")
    except Exception as e:
        rospy.logerr(f"Error during LED reset on shutdown: {e}")

    # 循环结束后可以发布终止消息（可选）
    rospy.loginfo(f"Vehicle {client_id} LED updates complete.")
