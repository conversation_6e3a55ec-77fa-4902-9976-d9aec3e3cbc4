#!/usr/bin/env python3

import rospy
from std_msgs.msg import Float32MultiArray
from std_msgs.msg import Int32

class LEDController:
    def __init__(self, colors, counts, color_list, pub):
        self.colors = colors
        self.counts = counts
        self.color_list = color_list
        self.pub = pub
        self.current_color_index = 0
        self.current_count = 0

    def update(self, tick):
        if tick % 10 == 0:  # 每10个tick更新一次颜色
            self.current_count += 1
            if self.current_count >= self.counts[self.current_color_index]:
                self.current_count = 0
                self.current_color_index = (self.current_color_index + 1) % len(self.colors)

            # 获取当前颜色
            color = self.colors[self.current_color_index]
            
            # 将颜色转换为RGB值
            r = (color >> 16) & 0xFF
            g = (color >> 8) & 0xFF
            b = color & 0xFF
            
            # 发布颜色消息
            msg = Float32MultiArray()
            msg.data = [r / 255.0, g / 255.0, b / 255.0]
            self.pub.publish(msg)
            
            # 存储颜色以供其他函数使用
            self.color_list.append(color)

def led_mode_callback(msg):
    global led_mode
    led_mode = msg.data
    rospy.loginfo(f"led_mode = {led_mode}")

if __name__ == "__main__":
    # 初始化ROS节点
    rospy.init_node("swarm_car_led_ctrl")

    # 创建发布者对象
    pub0 = rospy.Publisher("/swarm_car1/lower_led_ctrl", Float32MultiArray, queue_size=10)
    pub1 = rospy.Publisher("/swarm_car1/upper_led_ctrl", Float32MultiArray, queue_size=10)
    pub2 = rospy.Publisher("/swarm_car2/lower_led_ctrl", Float32MultiArray, queue_size=10)
    pub3 = rospy.Publisher("/swarm_car2/upper_led_ctrl", Float32MultiArray, queue_size=10)
    pub4 = rospy.Publisher("/swarm_car3/lower_led_ctrl", Float32MultiArray, queue_size=10)
    pub5 = rospy.Publisher("/swarm_car3/upper_led_ctrl", Float32MultiArray, queue_size=10)

    # 订阅LED模式
    rospy.Subscriber("/led_mode", Int32, led_mode_callback)
    led_mode = 0

    # 初始化颜色和闪烁标志变量
    color0, color1, color2, color3, color4, color5 = [], [], [], [], [], []

    # 配置LED控制器，红、绿、蓝色的显示次数
    led0_controller = LEDController([0xFF0000, 0xCCCCFF], [2, 1], color0, pub0)
    led1_controller = LEDController([0x00FF00, 0xCCCCFF], [2, 1], color1, pub1)
    led2_controller = LEDController([0x0000FF, 0xCCCCFF], [2, 1], color2, pub2)
    led3_controller = LEDController([0xCCCCFF, 0xCCCCFF], [2, 1], color3, pub3)
    led4_controller = LEDController([0xCCCCFF, 0xCCCCFF], [2, 1], color4, pub4)
    led5_controller = LEDController([0xCCCCFF, 0xCCCCFF], [2, 1], color5, pub5)

    tick = 0

    # 设置循环频率为3Hz
    rate = rospy.Rate(3)

    while not rospy.is_shutdown():
        # 根据LED模式选择不同的控制逻辑
        if led_mode == 0:
            led0_controller.update(tick)
            led1_controller.update(tick)
            led2_controller.update(tick)
            led3_controller.update(tick)
            led4_controller.update(tick)
            led5_controller.update(tick)
        elif led_mode == 1:
            # 模式1：所有LED同步闪烁红色
            msg = Float32MultiArray()
            if tick % 20 < 10:
                msg.data = [1.0, 0.0, 0.0]  # 红色
            else:
                msg.data = [0.0, 0.0, 0.0]  # 关闭
            pub0.publish(msg)
            pub1.publish(msg)
            pub2.publish(msg)
            pub3.publish(msg)
            pub4.publish(msg)
            pub5.publish(msg)
        elif led_mode == 2:
            # 模式2：所有LED同步闪烁绿色
            msg = Float32MultiArray()
            if tick % 20 < 10:
                msg.data = [0.0, 1.0, 0.0]  # 绿色
            else:
                msg.data = [0.0, 0.0, 0.0]  # 关闭
            pub0.publish(msg)
            pub1.publish(msg)
            pub2.publish(msg)
            pub3.publish(msg)
            pub4.publish(msg)
            pub5.publish(msg)
        elif led_mode == 3:
            # 模式3：所有LED同步闪烁蓝色
            msg = Float32MultiArray()
            if tick % 20 < 10:
                msg.data = [0.0, 0.0, 1.0]  # 蓝色
            else:
                msg.data = [0.0, 0.0, 0.0]  # 关闭
            pub0.publish(msg)
            pub1.publish(msg)
            pub2.publish(msg)
            pub3.publish(msg)
            pub4.publish(msg)
            pub5.publish(msg)

        tick += 1
        rate.sleep() 