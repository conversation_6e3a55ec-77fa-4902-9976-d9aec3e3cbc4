#!/usr/bin/env python3

import rospy
import socket
from std_msgs.msg import Float32MultiArray, Int32

# 全局变量
led_mode = 0

def led_mode_callback(msg):
    global led_mode
    led_mode = msg.data
    rospy.loginfo(f"Vehicle {socket.gethostname()}_car - LED mode: {led_mode} (No LED mode)")

if __name__ == "__main__":
    # 初始化ROS节点
    rospy.init_node("distributed_car_noled")

    # 获取客户端ID
    client_id = f'{socket.gethostname()}_car'
    
    # 创建订阅者对象，订阅 /led_mode 话题（但不执行LED控制）
    rospy.Subscriber("/led_mode", Int32, led_mode_callback)

    rospy.loginfo(f"Vehicle {client_id} - No LED mode initialized")

    # 保持节点运行
    rospy.spin()

    rospy.loginfo(f"Vehicle {client_id} - No LED mode terminated")
