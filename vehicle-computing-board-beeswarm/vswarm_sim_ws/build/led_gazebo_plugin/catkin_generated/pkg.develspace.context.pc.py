# generated from catkin/cmake/template/pkg.context.pc.in
CATKIN_PACKAGE_PREFIX = ""
PROJECT_PKG_CONFIG_INCLUDE_DIRS = "/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/src/led_gazebo_plugin/include".split(';') if "/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/src/led_gazebo_plugin/include" != "" else []
PROJECT_CATKIN_DEPENDS = "roscpp;gazebo_ros;std_msgs".replace(';', ' ')
PKG_CONFIG_LIBRARIES_WITH_PREFIX = "-lled_gazebo_plugin".split(';') if "-lled_gazebo_plugin" != "" else []
PROJECT_NAME = "led_gazebo_plugin"
PROJECT_SPACE_DIR = "/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/devel"
PROJECT_VERSION = "0.0.1"
