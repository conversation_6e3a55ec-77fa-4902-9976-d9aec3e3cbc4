set(_CATKIN_CURRENT_PACKAGE "vswarm_sim")
set(vswarm_sim_VERSION "0.0.0")
set(vswarm_sim_MAINTAINER "hypothesis <<EMAIL>>")
set(vswarm_sim_PACKAGE_FORMAT "2")
set(vswarm_sim_BUILD_DEPENDS "gazebo_plugins" "gazebo_ros" "gazebo_ros_control" "urdf" "xacro")
set(vswarm_sim_BUILD_EXPORT_DEPENDS "gazebo_plugins" "gazebo_ros" "gazebo_ros_control" "urdf" "xacro")
set(vswarm_sim_BUILDTOOL_DEPENDS "catkin")
set(vswarm_sim_BUILDTOOL_EXPORT_DEPENDS )
set(vswarm_sim_EXEC_DEPENDS "gazebo_plugins" "gazebo_ros" "gazebo_ros_control" "urdf" "xacro")
set(vswarm_sim_RUN_DEPENDS "gazebo_plugins" "gazebo_ros" "gazebo_ros_control" "urdf" "xacro")
set(vswarm_sim_TEST_DEPENDS )
set(vswarm_sim_DOC_DEPENDS )
set(vswarm_sim_URL_WEBSITE "")
set(vswarm_sim_URL_BUGTRACKER "")
set(vswarm_sim_URL_REPOSITORY "")
set(vswarm_sim_DEPRECATED "")