#!/usr/bin/env python3

import rospy
import cv2
import numpy as np
from sensor_msgs.msg import Image
from cv_bridge import CvBridge, CvBridgeError

class ImagePreprocessor:
    def __init__(self):
        self.bridge = CvBridge()
        
        # 创建图像发布者
        self.image_pub_car1_cam0 = rospy.Publisher("/swarm_car1/cam0/image_processed", Image, queue_size=1)
        self.image_pub_car1_cam1 = rospy.Publisher("/swarm_car1/cam1/image_processed", Image, queue_size=1)
        self.image_pub_car1_cam2 = rospy.Publisher("/swarm_car1/cam2/image_processed", Image, queue_size=1)
        self.image_pub_car1_cam3 = rospy.Publisher("/swarm_car1/cam3/image_processed", Image, queue_size=1)
        
        self.image_pub_car2_cam0 = rospy.Publisher("/swarm_car2/cam0/image_processed", Image, queue_size=1)
        self.image_pub_car2_cam1 = rospy.Publisher("/swarm_car2/cam1/image_processed", Image, queue_size=1)
        self.image_pub_car2_cam2 = rospy.Publisher("/swarm_car2/cam2/image_processed", Image, queue_size=1)
        self.image_pub_car2_cam3 = rospy.Publisher("/swarm_car2/cam3/image_processed", Image, queue_size=1)
        
        self.image_pub_car3_cam0 = rospy.Publisher("/swarm_car3/cam0/image_processed", Image, queue_size=1)
        self.image_pub_car3_cam1 = rospy.Publisher("/swarm_car3/cam1/image_processed", Image, queue_size=1)
        self.image_pub_car3_cam2 = rospy.Publisher("/swarm_car3/cam2/image_processed", Image, queue_size=1)
        self.image_pub_car3_cam3 = rospy.Publisher("/swarm_car3/cam3/image_processed", Image, queue_size=1)
        
        # 订阅原始图像
        self.image_sub_car1_cam0 = rospy.Subscriber("/cam0/image_raw", Image, self.callback_car1_cam0)
        self.image_sub_car1_cam1 = rospy.Subscriber("/cam1/image_raw", Image, self.callback_car1_cam1)
        self.image_sub_car1_cam2 = rospy.Subscriber("/cam2/image_raw", Image, self.callback_car1_cam2)
        self.image_sub_car1_cam3 = rospy.Subscriber("/cam3/image_raw", Image, self.callback_car1_cam3)
        
        self.image_sub_car2_cam0 = rospy.Subscriber("/swarm_car2/cam0/camera_node/image_raw", Image, self.callback_car2_cam0)
        self.image_sub_car2_cam1 = rospy.Subscriber("/swarm_car2/cam1/camera_node/image_raw", Image, self.callback_car2_cam1)
        self.image_sub_car2_cam2 = rospy.Subscriber("/swarm_car2/cam2/camera_node/image_raw", Image, self.callback_car2_cam2)
        self.image_sub_car2_cam3 = rospy.Subscriber("/swarm_car2/cam3/camera_node/image_raw", Image, self.callback_car2_cam3)
        
        self.image_sub_car3_cam0 = rospy.Subscriber("/swarm_car3/cam0/camera_node/image_raw", Image, self.callback_car3_cam0)
        self.image_sub_car3_cam1 = rospy.Subscriber("/swarm_car3/cam1/camera_node/image_raw", Image, self.callback_car3_cam1)
        self.image_sub_car3_cam2 = rospy.Subscriber("/swarm_car3/cam2/camera_node/image_raw", Image, self.callback_car3_cam2)
        self.image_sub_car3_cam3 = rospy.Subscriber("/swarm_car3/cam3/camera_node/image_raw", Image, self.callback_car3_cam3)
        
    def process_image(self, cv_image):
        # 在这里添加图像处理代码
        # 例如：转换为灰度图
        # gray_image = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
        # 或者添加其他处理步骤
        
        # 这里只是一个简单的示例，直接返回原始图像
        return cv_image
    
    def callback_car1_cam0(self, data):
        try:
            cv_image = self.bridge.imgmsg_to_cv2(data, "bgr8")
            processed_image = self.process_image(cv_image)
            self.image_pub_car1_cam0.publish(self.bridge.cv2_to_imgmsg(processed_image, "bgr8"))
        except CvBridgeError as e:
            print(e)
    
    def callback_car1_cam1(self, data):
        try:
            cv_image = self.bridge.imgmsg_to_cv2(data, "bgr8")
            processed_image = self.process_image(cv_image)
            self.image_pub_car1_cam1.publish(self.bridge.cv2_to_imgmsg(processed_image, "bgr8"))
        except CvBridgeError as e:
            print(e)
    
    def callback_car1_cam2(self, data):
        try:
            cv_image = self.bridge.imgmsg_to_cv2(data, "bgr8")
            processed_image = self.process_image(cv_image)
            self.image_pub_car1_cam2.publish(self.bridge.cv2_to_imgmsg(processed_image, "bgr8"))
        except CvBridgeError as e:
            print(e)
    
    def callback_car1_cam3(self, data):
        try:
            cv_image = self.bridge.imgmsg_to_cv2(data, "bgr8")
            processed_image = self.process_image(cv_image)
            self.image_pub_car1_cam3.publish(self.bridge.cv2_to_imgmsg(processed_image, "bgr8"))
        except CvBridgeError as e:
            print(e)
    
    def callback_car2_cam0(self, data):
        try:
            cv_image = self.bridge.imgmsg_to_cv2(data, "bgr8")
            processed_image = self.process_image(cv_image)
            self.image_pub_car2_cam0.publish(self.bridge.cv2_to_imgmsg(processed_image, "bgr8"))
        except CvBridgeError as e:
            print(e)
    
    def callback_car2_cam1(self, data):
        try:
            cv_image = self.bridge.imgmsg_to_cv2(data, "bgr8")
            processed_image = self.process_image(cv_image)
            self.image_pub_car2_cam1.publish(self.bridge.cv2_to_imgmsg(processed_image, "bgr8"))
        except CvBridgeError as e:
            print(e)
    
    def callback_car2_cam2(self, data):
        try:
            cv_image = self.bridge.imgmsg_to_cv2(data, "bgr8")
            processed_image = self.process_image(cv_image)
            self.image_pub_car2_cam2.publish(self.bridge.cv2_to_imgmsg(processed_image, "bgr8"))
        except CvBridgeError as e:
            print(e)
    
    def callback_car2_cam3(self, data):
        try:
            cv_image = self.bridge.imgmsg_to_cv2(data, "bgr8")
            processed_image = self.process_image(cv_image)
            self.image_pub_car2_cam3.publish(self.bridge.cv2_to_imgmsg(processed_image, "bgr8"))
        except CvBridgeError as e:
            print(e)
    
    def callback_car3_cam0(self, data):
        try:
            cv_image = self.bridge.imgmsg_to_cv2(data, "bgr8")
            processed_image = self.process_image(cv_image)
            self.image_pub_car3_cam0.publish(self.bridge.cv2_to_imgmsg(processed_image, "bgr8"))
        except CvBridgeError as e:
            print(e)
    
    def callback_car3_cam1(self, data):
        try:
            cv_image = self.bridge.imgmsg_to_cv2(data, "bgr8")
            processed_image = self.process_image(cv_image)
            self.image_pub_car3_cam1.publish(self.bridge.cv2_to_imgmsg(processed_image, "bgr8"))
        except CvBridgeError as e:
            print(e)
    
    def callback_car3_cam2(self, data):
        try:
            cv_image = self.bridge.imgmsg_to_cv2(data, "bgr8")
            processed_image = self.process_image(cv_image)
            self.image_pub_car3_cam2.publish(self.bridge.cv2_to_imgmsg(processed_image, "bgr8"))
        except CvBridgeError as e:
            print(e)
    
    def callback_car3_cam3(self, data):
        try:
            cv_image = self.bridge.imgmsg_to_cv2(data, "bgr8")
            processed_image = self.process_image(cv_image)
            self.image_pub_car3_cam3.publish(self.bridge.cv2_to_imgmsg(processed_image, "bgr8"))
        except CvBridgeError as e:
            print(e)

def main():
    rospy.init_node('image_preprocessor', anonymous=True)
    preprocessor = ImagePreprocessor()
    try:
        rospy.spin()
    except KeyboardInterrupt:
        print("Shutting down")
    cv2.destroyAllWindows()

if __name__ == '__main__':
    main() 