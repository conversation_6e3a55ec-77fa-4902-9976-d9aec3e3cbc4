#! /usr/bin/env python3
import threading
import time
import socket
import json
import os
import cmd
import urllib.request
import base64

import rospy
from gazebo_msgs.srv import SpawnModel
from geometry_msgs.msg import Pose
from gazebo_msgs.msg import ModelState, ModelStates
from std_msgs.msg import Float32MultiArray, Int32

from paho.mqtt import client as mqtt_client

# broker = '*********'  # 实验室网络环境
# username = '00799df312a45f05'
# password = 'DY6XhJ7Rki4UEX8Q7lNdWo9AiamInQ9AxufjnlH48r8vC'

# # broker = '**************'  # 外部网络环境
# broker = '***********'  # 外部网络环境
# username = '7cd18c8f9e8a4176'
# password = 'aD3ORwPhe5b2y1eb3kc9CFIB2qiagOTdjdgIiPDEme4D'


broker = '*************'  # 外部网络环境
username = 'b7986fb66280bcee'
password = 'Mx9B2EVhH79BY783Zp6rPcrFHv70kQIqJ2i9B4EbdevZ3M'


port = 1883
keepalive = 60     # 与代理通信之间允许的最长时间段（以秒为单位）              

client_id = f'{socket.gethostname()}'

class DistributedLEDController:
    """分布式LED控制器类，实现主从控制模式"""

    def __init__(self, client_id, mqtt_client):
        self.client_id = client_id
        self.mqtt_client = mqtt_client
        self.is_master = (client_id == "VCAR01_car")  # 指定主控车辆
        self.master_id = "VCAR01_car"
        self.led_mode = 0
        self.last_broadcast_time = 0
        self.broadcast_interval = 1.0  # 广播间隔（秒）

        # 创建节点特定的ROS发布者
        try:
            node_name = client_id.replace('_car', '')
            led_topic = f"/{node_name}/led_mode"
            self.led_mode_pub = rospy.Publisher(led_topic, Int32, queue_size=10)
            time.sleep(0.1)  # 等待发布者初始化
            print(f"DistributedLEDController created publisher: {led_topic}")
        except:
            self.led_mode_pub = None

        print(f"DistributedLEDController initialized for {client_id}, Master: {self.is_master}")

    def set_led_mode(self, mode):
        """设置LED模式"""
        if self.is_master:
            # 主控节点：广播给所有从节点
            self.led_mode = mode
            self.broadcast_led_command(mode)
            self.local_led_control(mode)
        else:
            # 从节点：只能本地设置（通常由主控节点触发）
            self.led_mode = mode
            self.local_led_control(mode)

    def broadcast_led_command(self, mode):
        """主控节点广播LED命令"""
        if not self.is_master:
            print(f"Warning: Non-master node {self.client_id} attempted to broadcast")
            return

        current_time = time.time()
        # 避免频繁广播
        if current_time - self.last_broadcast_time < self.broadcast_interval:
            return

        try:
            led_cmd = {
                'cmd_type': 'led_mode_master',
                'led_mode': mode,
                'master_id': self.client_id,
                'timestamp': current_time
            }

            # 广播给所有节点
            self.mqtt_client.publish('/Broadcast/led_master_cmd', json.dumps(led_cmd))
            self.last_broadcast_time = current_time
            print(f"Master {self.client_id} broadcasted LED mode: {mode}")

        except Exception as e:
            print(f"Error broadcasting LED command: {e}")

    def handle_master_command(self, cmd_msg):
        """处理主控节点的LED命令"""
        try:
            if self.is_master:
                return  # 主控节点忽略自己的广播

            if cmd_msg.get('cmd_type') == 'led_mode_master':
                master_id = cmd_msg.get('master_id')
                led_mode = cmd_msg.get('led_mode', 0)
                timestamp = cmd_msg.get('timestamp', 0)

                # 验证主控节点身份
                if master_id == self.master_id:
                    print(f"Slave {self.client_id} received master command: mode {led_mode}")
                    self.set_led_mode(led_mode)
                else:
                    print(f"Warning: Received command from unauthorized master: {master_id}")

        except Exception as e:
            print(f"Error handling master command: {e}")

    def local_led_control(self, mode):
        """本地LED控制"""
        try:
            if self.led_mode_pub:
                msg = Int32()
                msg.data = mode
                self.led_mode_pub.publish(msg)
                print(f"Published LED mode {mode} to local ROS")
        except Exception as e:
            print(f"Error in local LED control: {e}")

    def get_status(self):
        """获取控制器状态"""
        return {
            'client_id': self.client_id,
            'is_master': self.is_master,
            'led_mode': self.led_mode,
            'master_id': self.master_id
        }

class mqtt_client_thread():
    def __init__(self, broker, port, keepalive, client_id):
        super(mqtt_client_thread, self).__init__()
        self.broker = broker  # mqtt代理服务器地址
        self.port = port
        self.keepalive = keepalive 
        self.client_id = client_id
        self.recv_topic = '/simserver/recv'
        self.send_topic = '/simserver/send'
        self.reg_topic = '/simserver/register'
        self.agent_dict = {}
        
        self.client = self.connect_mqtt()
        self.client.on_message = self.mqtt_callback

        # 初始化分布式LED控制器
        self.led_controller = DistributedLEDController(self.client_id, self)
        
    def publish(self, topic, msg):
        result = self.client.publish(topic, msg)
        status = result[0]
        if status == 0:
            pass
            #print(f"Send `{msg}` to topic `{topic}`")
        else:
            print(f"Failed to send message to topic {topic}")  
                  
    def get_agent(self):
        client_url = f'http://{broker}:18083/api/v5/clients'
        req = urllib.request.Request(client_url)
        req.add_header('Content-Type', 'application/json')

        auth_header = "Basic " + base64.b64encode((username + ":" + password).encode()).decode()
        req.add_header('Authorization', auth_header)

        with urllib.request.urlopen(req) as response:
            client_data = json.loads(response.read().decode())
        
        agent_list = []
        for client in client_data['data']:
            if '_car' in client['clientid']:
                agent_list.append(f"/{client['clientid']}")
        return agent_list
       
    def set_subscribe(self):
        self.client.subscribe('/simserver/send')
        print("Subscribed to /simserver/send topic")

        # 订阅LED控制相关话题
        self.client.subscribe(f'/{self.client_id}/cmd')  # 单播LED命令
        self.client.subscribe('/Broadcast/cmd')  # 广播LED命令
        self.client.subscribe('/Broadcast/led_master_cmd')  # 主控LED命令
        print(f"Subscribed to LED control topics for {self.client_id}")
        
    def connect_mqtt(self):
        '''连接mqtt代理服务器'''
        def on_connect(client, userdata, flags, rc):
            '''连接回调函数'''
            # 响应状态码为0表示连接成功
            if rc == 0:
                print("Connected to MQTT OK!")
                # 连接成功后立即订阅
                client.subscribe('/simserver/send')
                client.subscribe(f'/{self.client_id}/cmd')  # 单播LED命令
                client.subscribe('/Broadcast/cmd')  # 广播LED命令
                client.subscribe('/Broadcast/led_master_cmd')  # 主控LED命令
                print("Auto-subscribed to all control topics on connect")
            else:
                print("Failed to connect, return code %d", rc)
        # 连接mqtt代理服务器，并获取连接引用
        client = mqtt_client.Client(self.client_id)
        client.on_connect = on_connect
        client.connect(self.broker, self.port, self.keepalive)
        return client          
    def mqtt_callback(self, client, userdata, msg):
        '''订阅消息回调函数'''
        if msg.topic == '/simserver/send':
            rev_msg = json.loads(msg.payload.decode())
            # 只有当agent_dict发生变化时才打印和更新
            if rev_msg != self.agent_dict:
                self.agent_dict = rev_msg
                print(f"Agent dict updated: {self.agent_dict}")
            else:
                self.agent_dict = rev_msg  # 静默更新

        # 处理单播LED控制命令
        elif msg.topic == f'/{self.client_id}/cmd':
            try:
                cmd_msg = json.loads(msg.payload.decode())
                if cmd_msg.get('cmd_type') in ['ledup', 'leddown']:
                    print(f"Received LED command: {cmd_msg}")
                    self.handle_led_command(cmd_msg)
                elif cmd_msg.get('cmd_type') == 'motion':
                    print(f"Received motion command: {cmd_msg}")
                    self.handle_motion_command(cmd_msg)
            except Exception as e:
                print(f"Error processing command: {e}")

        # 处理广播LED控制命令
        elif msg.topic == '/Broadcast/cmd':
            try:
                cmd_msg = json.loads(msg.payload.decode())
                if cmd_msg.get('cmd_type') in ['ledup', 'leddown']:
                    print(f"Received broadcast LED command: {cmd_msg}")
                    self.handle_led_command(cmd_msg)
            except Exception as e:
                print(f"Error processing broadcast LED command: {e}")

        # 处理主控LED命令
        elif msg.topic == '/Broadcast/led_master_cmd':
            try:
                cmd_msg = json.loads(msg.payload.decode())
                if cmd_msg.get('cmd_type') == 'led_mode_master':
                    print(f"Received master LED command: {cmd_msg}")
                    self.handle_master_led_command(cmd_msg)
            except Exception as e:
                print(f"Error processing master LED command: {e}")

    def handle_led_command(self, cmd_msg):
        '''处理LED控制命令'''
        try:
            # 将MQTT LED命令转换为ROS消息
            if cmd_msg['cmd_type'] == 'ledup':
                # 发布到上LED控制话题
                self.publish_led_command('upper', cmd_msg['args'])
            elif cmd_msg['cmd_type'] == 'leddown':
                # 发布到下LED控制话题
                self.publish_led_command('lower', cmd_msg['args'])
        except Exception as e:
            print(f"Error handling LED command: {e}")

    def handle_motion_command(self, cmd_msg):
        '''处理运动控制命令'''
        try:
            # 这里可以添加运动控制逻辑
            print(f"Motion command received: linear_x={cmd_msg['args']['0']}, angular_z={cmd_msg['args']['3']}")
        except Exception as e:
            print(f"Error handling motion command: {e}")

    def handle_master_led_command(self, cmd_msg):
        '''处理主控LED命令'''
        try:
            # 使用分布式LED控制器处理主控命令
            self.led_controller.handle_master_command(cmd_msg)
        except Exception as e:
            print(f"Error handling master LED command: {e}")

    def publish_led_command(self, position, args):
        '''发布LED控制命令到ROS'''
        try:
            # 提取RGB颜色值
            color_value = args.get('0', 0)

            # 将MQTT LED命令转换为LED模式
            # 根据颜色值设置不同的LED模式
            led_mode = self.color_to_led_mode(color_value)

            # 发布LED模式到分布式LED控制节点
            self.publish_led_mode(led_mode)

            print(f"Published LED command: {position} - color 0x{color_value:06X} -> mode {led_mode}")

        except Exception as e:
            print(f"Error publishing LED command: {e}")

    def color_to_led_mode(self, color_value):
        '''将颜色值映射到LED模式'''
        # 根据颜色值确定LED模式
        if color_value == 0xFF0000:  # 红色
            return 1
        elif color_value == 0x00FF00:  # 绿色
            return 2
        elif color_value == 0x0000FF:  # 蓝色
            return 3
        elif color_value == 0xFFFF00:  # 黄色
            return 4
        elif color_value == 0xFF00FF:  # 紫色
            return 5
        else:
            return 0  # 默认模式

    def publish_led_mode(self, mode):
        '''发布LED模式到本地节点特定的ROS话题'''
        try:
            if not hasattr(self, 'led_mode_pub'):
                # 获取节点名称并创建节点特定的发布者
                node_name = self.client_id.replace('_car', '')
                led_topic = f"/{node_name}/led_mode"
                self.led_mode_pub = rospy.Publisher(led_topic, Int32, queue_size=10)
                time.sleep(0.1)  # 等待发布者初始化
                rospy.loginfo(f"Created LED mode publisher: {led_topic}")

            msg = Int32()
            msg.data = mode
            self.led_mode_pub.publish(msg)
            print(f"Published LED mode: {mode}")

        except Exception as e:
            print(f"Error publishing LED mode: {e}")
    def run(self):
        self.client.loop_forever()

class user_cmd_input(cmd.Cmd):
    intro = ">>Welcome to the CLI. Type 'help' to list commands."
    prompt = ">>"
    def __init__(self, client):
        super(user_cmd_input, self).__init__()
        self.client = client
                        
    def do_ls(self, args):
        """\n 查看在线设备\n-t:显示订阅话题\n-l:显示设备功能\n"""
        self.agent_list = self.client.get_agent()
        for i, agent in enumerate(self.agent_list):
            print(f"{i}.{agent[1:]}")
        
    def do_quit(self, arg):
        """Exit the CLI."""
        print("Exiting...")
        os._exit(0)
    
    def do_msg(self, arg):
        print(self.client.agent_dict)

    def do_led(self, args):
        """\n LED控制命令\n-m <mode>: 设置LED模式 (0-3)\n-s: 显示LED状态\n"""
        if not args:
            print("LED控制命令:")
            print("  led -m <mode>  : 设置LED模式 (0-3)")
            print("  led -s         : 显示LED状态")
            return

        try:
            args = args.split()
            if args[0] == "-m" and len(args) == 2:
                mode = int(args[1])
                if 0 <= mode <= 3:
                    self.client.led_controller.set_led_mode(mode)
                    print(f"LED模式设置为: {mode}")
                else:
                    print("LED模式必须在0-3之间")
            elif args[0] == "-s":
                status = self.client.led_controller.get_status()
                print(f"LED控制器状态:")
                print(f"  客户端ID: {status['client_id']}")
                print(f"  是否为主控: {status['is_master']}")
                print(f"  当前LED模式: {status['led_mode']}")
                print(f"  主控节点ID: {status['master_id']}")
            else:
                print("无效的LED命令参数")
        except ValueError:
            print("LED模式必须是数字")
        except Exception as e:
            print(f"LED命令执行错误: {e}")

    def do_test_led(self, args):
        """\n 测试LED通信\n-b: 广播测试\n-u: 单播测试\n"""
        if not args:
            print("测试LED通信:")
            print("  test_led -b  : 广播LED测试")
            print("  test_led -u  : 单播LED测试")
            return

        try:
            if args == "-b":
                # 测试广播LED命令
                test_cmd = {
                    'cmd_type': 'ledup',
                    'args_length': 6,
                    'args': {
                        '0': 0xFF0000,  # 红色
                        '1': 14,
                        '2': 0xFF0000,
                        '3': 14,
                        '4': 0xFF0000,
                        '5': 14,
                    }
                }
                self.client.publish('/Broadcast/cmd', json.dumps(test_cmd))
                print("已发送广播LED测试命令 (红色)")

            elif args == "-u":
                # 测试单播LED命令
                test_cmd = {
                    'cmd_type': 'leddown',
                    'args_length': 6,
                    'args': {
                        '0': 0x00FF00,  # 绿色
                        '1': 14,
                        '2': 0x00FF00,
                        '3': 14,
                        '4': 0x00FF00,
                        '5': 14,
                    }
                }
                self.client.publish(f'/{self.client.client_id}/cmd', json.dumps(test_cmd))
                print("已发送单播LED测试命令 (绿色)")
            else:
                print("无效的测试参数")
        except Exception as e:
            print(f"LED测试命令执行错误: {e}")

    #空发送时执行命令
    def emptyline(self):
        return
    #找不到指令
    def default(self, line):
        self.stdout.write('%s:not found\n'%line)
        
        
class gazebo_node_thread():
    def __init__(self, client):
        try:
            rospy.init_node('sim_car', anonymous=True)
            self.mqtt_client = client
            self.set_model_states = rospy.Publisher('/gazebo/set_model_state', ModelState, queue_size=10)
            print("Connected to ROS OK!")
            self.model_list = []
            self.model_state_publishers = {}  # 动态为每个无人车创建一个发布器
            self.model_states_dict = {}  # 记录每个无人车的状态

            # LED状态管理
            self.last_led_mode = 0
            self.led_mode = 0

            # 位置变量
            self.px = 0.0
            self.py = 0.0
            self.pz = 0.0

            # 主从控制设置
            self.is_master = (client.client_id == "VCAR01_car")  # VCAR01为主控车辆
            self.master_id = "VCAR01_car"

            # 创建节点特定的LED相关发布者
            node_name = client.client_id.replace('_car', '')
            led_topic = f"/{node_name}/led_mode"
            self.model_led_publishers = rospy.Publisher(led_topic, Int32, queue_size=10)
            rospy.loginfo(f"Created model LED publisher: {led_topic}")

            # LED状态同步
            self.led_sync_timestamp = 0

            print(f"Vehicle {client.client_id} initialized. Master mode: {self.is_master}")

        except Exception as e:
            print(f"Connected to ROS Failed: {e}")
            pass
        
    def led_callback(self, msg):
        # 将LED模式存入变量
        old_led_mode = self.led_mode
        self.led_mode = msg.data

        # 🔧 修复：增强LED状态变化日志
        if old_led_mode != self.led_mode:
            rospy.loginfo(f"🔄 {self.mqtt_client.client_id} LED mode changed: {old_led_mode} -> {self.led_mode}")
            if self.led_mode == 0:
                rospy.loginfo(f"🔄 {self.mqtt_client.client_id} LED reset to default state")

        # 立即通过MQTT发送LED状态变化（所有节点都发送）
        if old_led_mode != self.led_mode:
            self.send_led_status_immediately()

        # 如果是主控节点且LED模式发生变化，广播给其他节点
        if self.is_master and old_led_mode != self.led_mode:
            self.broadcast_master_led_command(self.led_mode)

    def send_led_status_immediately(self):
        '''立即发送LED状态变化'''
        try:
            client_id = self.mqtt_client.client_id
            rev_msg = {
                'data_type': 'transform',
                'agent': client_id,
                'args': {
                    'pos_x': self.px,
                    'pos_y': self.py,
                    'pos_z': self.pz,
                    'led_mode': self.led_mode
                }
            }
            self.mqtt_client.publish(self.mqtt_client.recv_topic, json.dumps(rev_msg))
            rospy.loginfo(f"Immediately sent LED status for {client_id}: mode {self.led_mode}")
        except Exception as e:
            rospy.logerr(f"Error sending immediate LED status: {e}")

    def publish_other_node_led_status(self, node_name, led_mode):
        '''通知本地LED控制器为其他节点设置LED模式'''
        try:
            # 创建其他节点LED模式发布者
            if not hasattr(self, 'other_node_led_mode_publishers'):
                self.other_node_led_mode_publishers = {}

            # 为每个其他节点创建LED模式发布者
            if node_name not in self.other_node_led_mode_publishers:
                # 发布到本地LED控制器的其他节点LED模式话题
                topic_name = f"/other_node_led_mode/{node_name}"
                self.other_node_led_mode_publishers[node_name] = rospy.Publisher(topic_name, Int32, queue_size=10)
                time.sleep(0.1)  # 等待发布者初始化
                rospy.loginfo(f"Created other node LED mode publisher: {topic_name}")

            # 发布其他节点的LED模式到本地LED控制器
            msg = Int32()
            msg.data = led_mode
            self.other_node_led_mode_publishers[node_name].publish(msg)

            rospy.loginfo(f"Notified local LED controller: {node_name} LED mode {led_mode}")

        except Exception as e:
            rospy.logerr(f"Error notifying LED controller for {node_name}: {e}")

    def led_mode_to_color(self, mode):
        '''将LED模式转换为颜色值'''
        color_map = {
            0: 0xCCCCFF,  # 默认淡蓝色
            1: 0xFF0000,  # 红色
            2: 0x00FF00,  # 绿色
            3: 0x0000FF,  # 蓝色
            4: 0xFFFF00,  # 黄色
            5: 0xFF00FF,  # 紫色
        }
        return color_map.get(mode, 0xCCCCFF)

    def broadcast_master_led_command(self, led_mode):
        '''主控节点广播LED命令'''
        try:
            master_cmd = {
                'cmd_type': 'led_mode_master',
                'led_mode': led_mode,
                'master_id': self.mqtt_client.client_id,
                'timestamp': time.time()
            }

            # 广播给所有节点
            self.mqtt_client.publish('/Broadcast/led_master_cmd', json.dumps(master_cmd))
            rospy.loginfo(f"Master {self.mqtt_client.client_id} broadcasted LED mode: {led_mode}")

        except Exception as e:
            rospy.logerr(f"Error broadcasting master LED command: {e}")

    def handle_led_mode_sync(self, source_agent, led_mode, timestamp):
        '''处理LED模式同步'''
        try:
            # 避免重复处理相同的同步消息
            if timestamp > self.led_sync_timestamp:
                self.led_sync_timestamp = timestamp

                # 如果不是主控节点，接受同步
                if not self.is_master:
                    old_mode = self.led_mode
                    self.led_mode = led_mode

                    # 发布到本地ROS
                    msg = Int32()
                    msg.data = led_mode
                    self.model_led_publishers.publish(msg)

                    rospy.loginfo(f"Synced LED mode from {source_agent}: {old_mode} -> {led_mode}")

        except Exception as e:
            rospy.logerr(f"Error handling LED mode sync: {e}")

    def set_led_mode_as_master(self, mode):
        '''主控节点设置LED模式'''
        if self.is_master:
            self.led_mode = mode

            # 本地发布
            msg = Int32()
            msg.data = mode
            self.model_led_publishers.publish(msg)

            # 广播给其他节点
            self.broadcast_master_led_command(mode)

            rospy.loginfo(f"Master set LED mode: {mode}")
        else:
            rospy.logwarn(f"Non-master node {self.mqtt_client.client_id} attempted to set master LED mode")

    def get_model_states(self, msg):
        mycarIndex = msg.name.index(self.model_name)
        carPos = msg.pose[mycarIndex]

        self.px = carPos.position.x
        self.py = carPos.position.y
        self.pz = carPos.position.z
        # self.qx = carPos.orientation.x
        # self.qy = carPos.orientation.y
        # self.qz = carPos.orientation.z
        # self.qw = carPos.orientation.w
       # 遍历所有模型
        for i, model_name in enumerate(msg.name):
            if model_name in self.model_list:
                # 获取无人车的位置和姿态
                carPos = msg.pose[i]

                # 更新模型状态
                self.model_states_dict[model_name] = {
                    'pos_x': carPos.position.x,
                    'pos_y': carPos.position.y,
                    'pos_z': carPos.position.z,
                    # 'q_x': carPos.orientation.x,
                    # 'q_y': carPos.orientation.y,
                    # 'q_z': carPos.orientation.z,
                    # 'q_w': carPos.orientation.w,
                }

    
    def sim_init(self, pos):
        # 获取 SDF 文件路径
        swarm_car = rospy.get_param('/swarm_car')
        
        # 读取 SDF 文件内容
        with open(swarm_car, 'r') as sdf_file:
            sdf_content = sdf_file.read()

        # 获取/robot_description参数的值
        # robot_description = rospy.get_param('/robot_description')
        # 模型名称和命名空间
        model_name = client_id
        model_namespace = ''
        # 初始姿态
        initial_pose = Pose()
        initial_pose.position.x = pos['pos_x']
        initial_pose.position.y = pos['pos_y']
        initial_pose.position.z = pos['pos_z']

        # 使用 gazebo/spawn_sdf_model 服务来加载 SDF 模型
        rospy.wait_for_service('/gazebo/spawn_sdf_model')
        try:
            spawn_sdf_model = rospy.ServiceProxy('/gazebo/spawn_sdf_model', SpawnModel)
            spawn_sdf_model(model_name, sdf_content, model_namespace, initial_pose, "world")
            rospy.loginfo(f"Spawned {model_name} successfully!")
        except rospy.ServiceException as e:
            rospy.logerr(f"Spawn service failed: {e}")

        return model_name
    
    def add_agent(self, pos, id_):
        # 获取/swarm_car_no_pub参数的值
        swarm_car_no_pub = rospy.get_param('/swarm_car_no_pub')
        # 读取 SDF 文件内容
        with open(swarm_car_no_pub, 'r') as sdf_file:
            sdf_content = sdf_file.read()
        # 模型名称和命名空间
        model_name = id_
        model_namespace = id_
        # 初始姿态
        initial_pose = Pose()
        initial_pose.position.x = pos['pos_x']
        initial_pose.position.y = pos['pos_y']
        initial_pose.position.z = pos['pos_z']

        # 使用 gazebo/spawn_sdf_model 服务来加载 SDF 模型
        rospy.wait_for_service('/gazebo/spawn_sdf_model')
        try:
            spawn_sdf_model = rospy.ServiceProxy('/gazebo/spawn_sdf_model', SpawnModel)
            spawn_sdf_model(model_name, sdf_content, model_namespace, initial_pose, "world")
            rospy.loginfo(f"Spawned {model_name} successfully!")
        except rospy.ServiceException as e:
            rospy.logerr(f"Spawn service failed: {e}")

        return model_name
    
    def spawn_model(self, model_xml, model_name, model_namespace, initial_pose):
        rospy.wait_for_service('/gazebo/spawn_urdf_model')
        try:
            spawn_model_prox = rospy.ServiceProxy('/gazebo/spawn_urdf_model', SpawnModel)
            spawn_model_prox(model_name, model_xml, model_namespace, initial_pose, "world")
            rospy.loginfo("Spawned model: %s", model_name)
        except rospy.ServiceException as e:
            rospy.logerr("Spawn model service call failed: %s", e)
                
    def run(self):
        #初始化注册
        reg_msg = {
            'live': True,
            'agent': client_id,
        } 
        reg_state = False
        self.mqtt_client.publish(self.mqtt_client.reg_topic, json.dumps(reg_msg))
        self.model_led_publishers = rospy.Publisher("/led_mode", Int32, queue_size=10)

        ros_hz = 50
        rate = rospy.Rate(ros_hz)
        while not rospy.is_shutdown():
            try:
                if (not reg_state) and (client_id in self.mqtt_client.agent_dict):
                    reg_state = True
                    self.model_name = self.sim_init(self.mqtt_client.agent_dict[client_id])
                    rospy.Subscriber("/gazebo/model_states",ModelStates,self.get_model_states,queue_size=10)

                    # 订阅本地节点特定的LED模式话题
                    node_name = client_id.replace('_car', '')  # 移除_car后缀获取节点名
                    local_led_topic = f"/{node_name}/led_mode"
                    rospy.Subscriber(local_led_topic, Int32, self.led_callback)
                    rospy.loginfo(f"Subscribed to local LED topic: {local_led_topic}")

                    print(f"Registered successfully! Agent dict: {self.mqtt_client.agent_dict}")
                if reg_state:
                    for each in self.mqtt_client.agent_dict:
                        if (each not in self.model_list) and (each != client_id):
                            print(f"Adding new agent: {each} to Gazebo")
                            self.model_list.append(each)
                            self.add_agent(self.mqtt_client.agent_dict[each], each)
                    try:
                        # 非主控节点的LED模式重置逻辑（可选）
                        # if not self.is_master:
                        #     self.led_mode = 0

                        rev_msg = {
                        'data_type': 'transform',
                        'agent': client_id,
                        'args':{'pos_x':self.px,
                                'pos_y':self.py,
                                'pos_z':self.pz,
                                # 'q_x':self.qx,
                                # 'q_y':self.qy,
                                # 'q_z':self.qz,
                                # 'q_w':self.qw,
                                'led_mode': self.led_mode
                                }
                        }
                        self.mqtt_client.publish(self.mqtt_client.recv_topic, json.dumps(rev_msg))
                    except:
                        pass
                    try:
                        # 主控节点LED模式发布逻辑
                        if self.is_master and (self.led_mode != self.last_led_mode):
                            msg = Int32()
                            msg.data = self.led_mode
                            self.model_led_publishers.publish(msg)
                            self.last_led_mode = self.led_mode
                            rospy.loginfo(f"Master {client_id} Published led_mode: {msg.data}")

                        # 在主循环中处理和发布模型状态
                        for each in self.model_list:
                            tmp_model_state = ModelState()
                            tmp_model_state.model_name = each
                            tmp_model_state.pose.position.x = self.mqtt_client.agent_dict[each]['pos_x']
                            tmp_model_state.pose.position.y = self.mqtt_client.agent_dict[each]['pos_y']
                            tmp_model_state.pose.position.z = self.mqtt_client.agent_dict[each]['pos_z']
                            tmp_model_state.pose.orientation.x = 0.0
                            tmp_model_state.pose.orientation.y = 0.0
                            tmp_model_state.pose.orientation.z = 0.0
                            tmp_model_state.pose.orientation.w = 1.0
                            self.set_model_states.publish(tmp_model_state)

                            # 实现真正的分布式LED状态可见性 - 每个节点都能看到其他节点的LED状态
                            if each in self.mqtt_client.agent_dict and each != client_id:
                                other_led_mode = self.mqtt_client.agent_dict[each].get('led_mode', 0)

                                # 初始化其他节点LED状态跟踪
                                if not hasattr(self, 'other_nodes_led_status'):
                                    self.other_nodes_led_status = {}

                                # 检查其他节点的LED状态是否发生变化
                                if each not in self.other_nodes_led_status:
                                    self.other_nodes_led_status[each] = other_led_mode
                                    rospy.loginfo(f"Node {client_id} initialized tracking for {each} LED mode: {other_led_mode}")
                                elif self.other_nodes_led_status[each] != other_led_mode:
                                    old_mode = self.other_nodes_led_status[each]
                                    self.other_nodes_led_status[each] = other_led_mode
                                    rospy.loginfo(f"Node {client_id} observed {each} LED change: {old_mode} -> {other_led_mode}")

                                    # 关键修复：为其他节点创建独立的LED控制话题发布者
                                    self.publish_other_node_led_status(each, other_led_mode)

                                # 主从同步逻辑（保持原有功能）
                                if (not self.is_master) and (each == self.master_id):
                                    if (other_led_mode != self.last_led_mode):
                                        rospy.loginfo(f"Slave {client_id} syncing master {each} LED mode: {self.last_led_mode} -> {other_led_mode}")
                                        msg = Int32()
                                        msg.data = other_led_mode
                                        self.model_led_publishers.publish(msg)
                                        self.last_led_mode = other_led_mode

                    except Exception as e:
                        rospy.logwarn(f"Error in LED control loop: {e}")
                        pass
                #print(self.mqtt_client.agent_dict)
                # 为每个模型创建独立的ROS发布器
                for each in self.mqtt_client.agent_dict:
                    """为每个无人车创建一个ROS发布器"""
                    model_name = each
                    topic_name = f"/{model_name}/model_states"  # 根据model_name创建独立的话题
                    self.model_state_publishers[model_name] = rospy.Publisher(topic_name, Pose, queue_size=10)
                    """将每个无人车的位置和姿态发布到各自的ROS话题"""
                    if model_name in self.mqtt_client.agent_dict:
                        state = self.mqtt_client.agent_dict[model_name]
                        pose_msg = Pose()
                        pose_msg.position.x = state['pos_x']
                        pose_msg.position.y = state['pos_y']
                        pose_msg.position.z = state['pos_z']
                        pose_msg.orientation.x = 0.0
                        pose_msg.orientation.y = 0.0
                        pose_msg.orientation.z = 0.0
                        pose_msg.orientation.w = 1.0

                        # 发布到相应的ROS话题
                        if model_name in self.model_state_publishers:
                            self.model_state_publishers[model_name].publish(pose_msg)

                rate.sleep()
            except rospy.ROSInterruptException:
                rospy.logerr("ROS Interrupt Exception! Just ignore the exception!")
            except rospy.ROSTimeMovedBackwardsException:
                rospy.logerr("ROS Time Backwards! Just ignore the exception!")
            
if __name__ == "__main__":
    
    # 启动MQTT客户端的线程
    mqtt_client_instance = mqtt_client_thread(broker=broker, 
                                           port=port, 
                                           keepalive=keepalive, 
                                           client_id=client_id + '_car'
                                           )
    mqtt_thread = threading.Thread(target=mqtt_client_instance.run)
    mqtt_thread.start()
    client = mqtt_client_instance
    client.set_subscribe()
    time.sleep(1)
    ros_thread_instance = gazebo_node_thread(client=client)
    ros_thread = threading.Thread(target=ros_thread_instance.run)
    ros_thread.start()
    # 启动Cli线程 
    time.sleep(1)
    user_cmd_instance = user_cmd_input(client=client)
    cli_thread = threading.Thread(target=user_cmd_instance.cmdloop)
    cli_thread.start() 