#!/usr/bin/env sh
# generated from catkin/python/catkin/environment_cache.py

# based on a snapshot of the environment before and after calling the setup script
# it emulates the modifications of the setup script without recurring computations

# new environment variables

# modified environment variables
export LD_LIBRARY_PATH='/opt/ros/noetic/lib:/usr/local/cuda-12.1/lib64:/usr/local/cuda-12.1/lib64'
export PKG_CONFIG_PATH='/opt/ros/noetic/lib/pkgconfig'
export PWD='/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/build'
export PYTHONPATH='/opt/ros/noetic/lib/python3/dist-packages'