<?xml version="1.0"?>
<package format="2">
  <name>cpp_version</name>
  <version>1.0.0</version>
  <description>
    BeeSwarm分布式无人车系统的C++高性能实现版本。
    提供MQTT通信、LED控制、位置控制、图像处理等核心功能，
    相比Python版本具有更低的资源消耗和更高的实时性能。
  </description>

  <maintainer email="<EMAIL>">BeeSwarm Team</maintainer>
  <license>MIT</license>

  <url type="website">https://github.com/beeswarm/vehicle-computing-board-cpp</url>
  <url type="bugtracker">https://github.com/beeswarm/vehicle-computing-board-cpp/issues</url>
  <url type="repository">https://github.com/beeswarm/vehicle-computing-board-cpp</url>

  <author email="<EMAIL>">BeeSwarm Team</author>

  <!-- 构建工具依赖 -->
  <buildtool_depend>catkin</buildtool_depend>

  <!-- 构建依赖 -->
  <build_depend>roscpp</build_depend>
  <build_depend>rospy</build_depend>
  <build_depend>std_msgs</build_depend>
  <build_depend>geometry_msgs</build_depend>
  <build_depend>sensor_msgs</build_depend>
  <build_depend>gazebo_msgs</build_depend>
  <build_depend>cv_bridge</build_depend>
  <build_depend>image_transport</build_depend>
  <build_depend>libopencv-dev</build_depend>
  <build_depend>libboost-all-dev</build_depend>

  <build_depend>libmosquitto-dev</build_depend>
  <build_depend>libyaml-cpp-dev</build_depend>
  <build_depend>nlohmann-json3-dev</build_depend>

  <!-- 构建导出依赖 -->
  <build_export_depend>roscpp</build_export_depend>
  <build_export_depend>std_msgs</build_export_depend>
  <build_export_depend>geometry_msgs</build_export_depend>
  <build_export_depend>sensor_msgs</build_export_depend>
  <build_export_depend>gazebo_msgs</build_export_depend>
  <build_export_depend>cv_bridge</build_export_depend>
  <build_export_depend>image_transport</build_export_depend>

  <!-- 运行时依赖 -->
  <exec_depend>roscpp</exec_depend>
  <exec_depend>rospy</exec_depend>
  <exec_depend>std_msgs</exec_depend>
  <exec_depend>geometry_msgs</exec_depend>
  <exec_depend>sensor_msgs</exec_depend>
  <exec_depend>gazebo_msgs</exec_depend>
  <exec_depend>cv_bridge</exec_depend>
  <exec_depend>image_transport</exec_depend>
  <exec_depend>libopencv-dev</exec_depend>
  <exec_depend>libboost-all-dev</exec_depend>

  <exec_depend>libmosquitto-dev</exec_depend>
  <exec_depend>libyaml-cpp-dev</exec_depend>
  <exec_depend>nlohmann-json3-dev</exec_depend>

  <!-- 测试依赖 -->
  <test_depend>rostest</test_depend>
  <test_depend>gtest</test_depend>

  <!-- 文档依赖 -->
  <doc_depend>doxygen</doc_depend>

  <!-- 导出信息 -->
  <export>
    <!-- 其他工具可以请求额外信息 -->
  </export>
</package>
