set(_CATKIN_CURRENT_PACKAGE "cpp_version")
set(cpp_version_VERSION "1.0.0")
set(cpp_version_MAINTAINER "BeeSwarm Team <<EMAIL>>")
set(cpp_version_PACKAGE_FORMAT "2")
set(cpp_version_BUILD_DEPENDS "roscpp" "rospy" "std_msgs" "geometry_msgs" "sensor_msgs" "gazebo_msgs" "cv_bridge" "image_transport" "libopencv-dev" "libboost-all-dev" "libmosquitto-dev" "libyaml-cpp-dev" "nlohmann-json3-dev")
set(cpp_version_BUILD_EXPORT_DEPENDS "roscpp" "std_msgs" "geometry_msgs" "sensor_msgs" "gazebo_msgs" "cv_bridge" "image_transport")
set(cpp_version_BUILDTOOL_DEPENDS "catkin")
set(cpp_version_BUILDTOOL_EXPORT_DEPENDS )
set(cpp_version_EXEC_DEPENDS "roscpp" "rospy" "std_msgs" "geometry_msgs" "sensor_msgs" "gazebo_msgs" "cv_bridge" "image_transport" "libopencv-dev" "libboost-all-dev" "libmosquitto-dev" "libyaml-cpp-dev" "nlohmann-json3-dev")
set(cpp_version_RUN_DEPENDS "roscpp" "rospy" "std_msgs" "geometry_msgs" "sensor_msgs" "gazebo_msgs" "cv_bridge" "image_transport" "libopencv-dev" "libboost-all-dev" "libmosquitto-dev" "libyaml-cpp-dev" "nlohmann-json3-dev")
set(cpp_version_TEST_DEPENDS "rostest" "gtest")
set(cpp_version_DOC_DEPENDS "doxygen")
set(cpp_version_URL_WEBSITE "https://github.com/beeswarm/vehicle-computing-board-cpp")
set(cpp_version_URL_BUGTRACKER "https://github.com/beeswarm/vehicle-computing-board-cpp/issues")
set(cpp_version_URL_REPOSITORY "https://github.com/beeswarm/vehicle-computing-board-cpp")
set(cpp_version_DEPRECATED "")