#!/usr/bin/env python3
"""
运动预测节点 - 预测目标和自身的未来位置
解决决策延迟导致的位置过时问题
"""

import rospy
import numpy as np
import math
from collections import defaultdict, deque
from std_msgs.msg import Header
from geometry_msgs.msg import Twist, PoseStamped
from swarm_msgs.msg import SpatialInfoArray, PredictedState, PredictedStateArray
from gazebo_msgs.srv import GetModelState
import tf.transformations as tf_trans

class MotionPredictorNode:
    """运动预测节点"""
    
    def __init__(self):
        rospy.init_node('motion_predictor_node', anonymous=True)
        
        # 配置参数
        self.config = {
            'prediction_horizon': 2.0,    # 预测时间窗口(秒)
            'prediction_steps': 10,       # 预测步数
            'history_window': 3.0,        # 历史数据窗口(秒)
            'min_history_points': 3,      # 最小历史点数
            'publish_rate': 20.0,         # 发布频率(Hz)
            'velocity_smoothing': 0.7     # 速度平滑系数
        }
        
        # 目标跟踪历史
        self.target_history = defaultdict(lambda: deque(maxlen=50))  # track_id -> [(timestamp, x, y, vx, vy)]
        self.self_history = deque(maxlen=50)  # [(timestamp, x, y, vx, vy, heading)]
        
        # 当前执行的指令
        self.current_command = None
        self.command_start_time = None
        
        # ROS接口
        self.prediction_pub = rospy.Publisher('/predicted_states', PredictedStateArray, queue_size=10)
        self.spatial_sub = rospy.Subscriber('/spatial_info', SpatialInfoArray, self._spatial_callback)
        self.vel_sub = rospy.Subscriber('/vel_cmd', Twist, self._velocity_callback)
        
        # Gazebo服务
        try:
            rospy.wait_for_service('/gazebo/get_model_state', timeout=5.0)
            self.get_model_state = rospy.ServiceProxy('/gazebo/get_model_state', GetModelState)
            self.use_gazebo = True
        except:
            rospy.logwarn("Gazebo服务不可用，使用估算位置")
            self.use_gazebo = False
        
        # 启动预测线程
        import threading
        self.prediction_active = True
        self.prediction_thread = threading.Thread(target=self._prediction_loop)
        self.prediction_thread.daemon = True
        self.prediction_thread.start()
        
        rospy.loginfo("🔮 运动预测节点初始化完成")
    
    def _spatial_callback(self, msg):
        """空间信息回调 - 更新目标历史"""
        current_time = rospy.Time.now().to_sec()
        
        for spatial in msg.spatial_infos:
            track_id = spatial.track_id
            
            # 计算目标在世界坐标系中的位置
            x = spatial.position_x
            y = spatial.position_y
            
            # 计算速度（基于历史位置）
            vx, vy = self._estimate_velocity(track_id, x, y, current_time)
            
            # 添加到历史记录
            self.target_history[track_id].append((current_time, x, y, vx, vy))
            
            # 清理过期数据
            self._cleanup_old_data(self.target_history[track_id], current_time)
    
    def _velocity_callback(self, msg):
        """速度指令回调 - 记录自身运动指令"""
        current_time = rospy.Time.now().to_sec()
        self.current_command = msg
        self.command_start_time = current_time
        
        # 更新自身状态历史
        self._update_self_state(current_time)
    
    def _update_self_state(self, current_time):
        """更新自身状态"""
        if self.use_gazebo:
            try:
                # 从Gazebo获取真实位置
                model_state = self.get_model_state(rospy.get_param('/robot_name', 'robot'), '')
                x = model_state.pose.position.x
                y = model_state.pose.position.y
                
                # 计算朝向
                orientation = model_state.pose.orientation
                euler = tf_trans.euler_from_quaternion([
                    orientation.x, orientation.y, orientation.z, orientation.w
                ])
                heading = euler[2]
                
                # 估算速度
                if len(self.self_history) > 0:
                    last_time, last_x, last_y, _, _, _ = self.self_history[-1]
                    dt = current_time - last_time
                    if dt > 0:
                        vx = (x - last_x) / dt
                        vy = (y - last_y) / dt
                    else:
                        vx = vy = 0.0
                else:
                    vx = vy = 0.0
                
                self.self_history.append((current_time, x, y, vx, vy, heading))
                
            except Exception as e:
                rospy.logwarn(f"获取自身状态失败: {e}")
        else:
            # 基于指令估算位置（简化版本）
            if len(self.self_history) > 0 and self.current_command:
                last_time, last_x, last_y, _, _, last_heading = self.self_history[-1]
                dt = current_time - last_time
                
                # 基于速度指令估算新位置
                vx = self.current_command.linear.x
                vy = self.current_command.linear.y
                x = last_x + vx * dt
                y = last_y + vy * dt
                heading = last_heading + self.current_command.angular.z * dt
                
                self.self_history.append((current_time, x, y, vx, vy, heading))
        
        # 清理过期数据
        self._cleanup_old_data(self.self_history, current_time)
    
    def _estimate_velocity(self, track_id, x, y, current_time):
        """估算目标速度"""
        history = self.target_history[track_id]
        
        if len(history) < 2:
            return 0.0, 0.0
        
        # 使用最近几个点计算平均速度
        recent_points = [p for p in history if current_time - p[0] <= 1.0]
        
        if len(recent_points) < 2:
            return 0.0, 0.0
        
        # 线性回归估算速度
        times = np.array([p[0] for p in recent_points])
        xs = np.array([p[1] for p in recent_points])
        ys = np.array([p[2] for p in recent_points])
        
        if len(times) >= 2:
            vx = np.polyfit(times, xs, 1)[0] if np.std(times) > 1e-6 else 0.0
            vy = np.polyfit(times, ys, 1)[0] if np.std(times) > 1e-6 else 0.0
        else:
            vx = vy = 0.0
        
        # 速度平滑
        if len(history) >= 2:
            last_vx, last_vy = history[-2][3], history[-2][4]
            alpha = self.config['velocity_smoothing']
            vx = alpha * last_vx + (1 - alpha) * vx
            vy = alpha * last_vy + (1 - alpha) * vy
        
        return vx, vy
    
    def _prediction_loop(self):
        """预测循环"""
        rate = rospy.Rate(self.config['publish_rate'])
        
        while self.prediction_active and not rospy.is_shutdown():
            try:
                self._generate_predictions()
            except Exception as e:
                rospy.logwarn(f"预测错误: {e}")
            
            rate.sleep()
    
    def _generate_predictions(self):
        """生成预测状态"""
        current_time = rospy.Time.now().to_sec()
        predictions = []
        
        # 预测目标状态
        for track_id, history in self.target_history.items():
            if len(history) >= self.config['min_history_points']:
                predicted_states = self._predict_target_trajectory(track_id, history, current_time)
                predictions.extend(predicted_states)
        
        # 预测自身状态
        if len(self.self_history) >= self.config['min_history_points']:
            self_predictions = self._predict_self_trajectory(current_time)
            predictions.extend(self_predictions)
        
        # 发布预测结果
        if predictions:
            prediction_array = PredictedStateArray()
            prediction_array.header = Header()
            prediction_array.header.stamp = rospy.Time.now()
            prediction_array.header.frame_id = "map"
            prediction_array.predictions = predictions
            
            self.prediction_pub.publish(prediction_array)
    
    def _predict_target_trajectory(self, track_id, history, current_time):
        """预测目标轨迹"""
        predictions = []
        
        # 获取最新状态
        latest_time, latest_x, latest_y, latest_vx, latest_vy = history[-1]
        
        # 预测未来状态
        dt = self.config['prediction_horizon'] / self.config['prediction_steps']
        
        for i in range(1, self.config['prediction_steps'] + 1):
            future_time = current_time + i * dt
            
            # 简单线性预测（可以改进为更复杂的模型）
            pred_x = latest_x + latest_vx * (i * dt)
            pred_y = latest_y + latest_vy * (i * dt)
            
            # 添加不确定性（基于历史数据的方差）
            uncertainty = self._calculate_uncertainty(history, i * dt)
            
            prediction = PredictedState()
            prediction.entity_type = "target"
            prediction.entity_id = str(track_id)
            prediction.predicted_time = future_time
            prediction.position_x = pred_x
            prediction.position_y = pred_y
            prediction.velocity_x = latest_vx
            prediction.velocity_y = latest_vy
            prediction.uncertainty_radius = uncertainty
            prediction.confidence = max(0.1, 1.0 - i * 0.1)  # 时间越远置信度越低
            
            predictions.append(prediction)
        
        return predictions
    
    def _predict_self_trajectory(self, current_time):
        """预测自身轨迹"""
        predictions = []
        
        if not self.self_history:
            return predictions
        
        # 获取最新状态
        latest_time, latest_x, latest_y, latest_vx, latest_vy, latest_heading = self.self_history[-1]
        
        # 考虑当前执行的指令
        if self.current_command and self.command_start_time:
            cmd_vx = self.current_command.linear.x
            cmd_vy = self.current_command.linear.y
            cmd_omega = self.current_command.angular.z
        else:
            cmd_vx = cmd_vy = cmd_omega = 0.0
        
        # 预测未来状态
        dt = self.config['prediction_horizon'] / self.config['prediction_steps']
        
        for i in range(1, self.config['prediction_steps'] + 1):
            future_time = current_time + i * dt
            
            # 基于运动模型预测
            pred_x = latest_x + cmd_vx * (i * dt)
            pred_y = latest_y + cmd_vy * (i * dt)
            pred_heading = latest_heading + cmd_omega * (i * dt)
            
            prediction = PredictedState()
            prediction.entity_type = "self"
            prediction.entity_id = "robot"
            prediction.predicted_time = future_time
            prediction.position_x = pred_x
            prediction.position_y = pred_y
            prediction.velocity_x = cmd_vx
            prediction.velocity_y = cmd_vy
            prediction.heading = pred_heading
            prediction.uncertainty_radius = 0.1 * i  # 自身预测不确定性较小
            prediction.confidence = 0.9
            
            predictions.append(prediction)
        
        return predictions
    
    def _calculate_uncertainty(self, history, time_ahead):
        """计算预测不确定性"""
        if len(history) < 3:
            return 0.5
        
        # 基于历史轨迹的变化计算不确定性
        positions = [(p[1], p[2]) for p in history[-5:]]  # 最近5个位置
        
        if len(positions) < 2:
            return 0.5
        
        # 计算轨迹的曲率和变化
        distances = []
        for i in range(1, len(positions)):
            dx = positions[i][0] - positions[i-1][0]
            dy = positions[i][1] - positions[i-1][1]
            distances.append(math.sqrt(dx*dx + dy*dy))
        
        if distances:
            avg_distance = np.mean(distances)
            std_distance = np.std(distances)
            uncertainty = (std_distance + 0.1) * time_ahead
            return min(uncertainty, 2.0)  # 限制最大不确定性
        
        return 0.5
    
    def _cleanup_old_data(self, data_deque, current_time):
        """清理过期数据"""
        while data_deque and current_time - data_deque[0][0] > self.config['history_window']:
            data_deque.popleft()
    
    def shutdown(self):
        """关闭节点"""
        self.prediction_active = False
        rospy.loginfo("🛑 运动预测节点关闭")

def main():
    try:
        node = MotionPredictorNode()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
    finally:
        if 'node' in locals():
            node.shutdown()

if __name__ == '__main__':
    main()
