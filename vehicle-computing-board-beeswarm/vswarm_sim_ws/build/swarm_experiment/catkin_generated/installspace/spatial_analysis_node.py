#!/usr/bin/env python3
"""
空间分析节点 - 解耦版本
负责计算目标的空间位置信息，异步发布空间数据
"""

import rospy
import threading
import time
import numpy as np
from collections import defaultdict, deque
from std_msgs.msg import Header
from swarm_msgs.msg import DetectionArray, SpatialInfo, SpatialInfoArray
from cal_vec import calculate_position_vector_planar, create_camera_matrix

class SpatialAnalysisNode:
    """空间分析节点"""

    def __init__(self):
        rospy.init_node('spatial_analysis_node', anonymous=True)

        # 配置参数
        self.config = {
            'real_width': 0.31,      # 目标实际宽度(米)
            'analysis_window': 3.0,   # 分析窗口时间(秒)
            'min_detections': 5,      # 最小检测次数
            'publish_rate': 10.0,     # Hz
            'max_distance': 10.0      # 最大有效距离(米)
        }

        # 摄像头朝向配置
        self.camera_orientations = {
            'cam0': 270,    # -X轴方向
            'cam1': 180,    # -Y轴方向
            'cam2': 90,     # +X轴方向
            'cam3': 0       # +Y轴方向
        }

        # 初始化相机矩阵
        self.camera_matrix = create_camera_matrix()
        self.camera_matrix_inv = np.linalg.inv(self.camera_matrix)

        # 数据缓存
        self.detection_buffer = defaultdict(lambda: defaultdict(deque))  # cam_name -> track_id -> deque
        self.buffer_lock = threading.Lock()

        # 发布器和订阅器
        self.spatial_pub = rospy.Publisher('/spatial_info', SpatialInfoArray, queue_size=10)
        self.detection_sub = rospy.Subscriber('/detection_results', DetectionArray, self._detection_callback)

        # 启动分析线程
        self.analysis_active = True
        self.analysis_thread = threading.Thread(target=self._analysis_loop)
        self.analysis_thread.daemon = True
        self.analysis_thread.start()

        rospy.loginfo("📐 空间分析节点初始化完成")
    
    def _detection_callback(self, msg):
        """检测结果回调"""
        current_time = time.time()
        
        with self.buffer_lock:
            for detection in msg.detections:
                cam_name = detection.camera_name
                track_id = detection.track_id
                
                # 只保留有效目标
                if detection.class_id != 110 and detection.width > 0:
                    detection_data = {
                        'x_center': detection.x_center,
                        'y_center': detection.y_center,
                        'width': detection.width,
                        'height': detection.height,
                        'confidence': detection.confidence,
                        'timestamp': current_time
                    }
                    
                    # 添加到缓存
                    self.detection_buffer[cam_name][track_id].append(detection_data)
                    
                    # 限制缓存大小
                    max_buffer_size = int(self.config['analysis_window'] * 30)  # 假设30fps
                    if len(self.detection_buffer[cam_name][track_id]) > max_buffer_size:
                        self.detection_buffer[cam_name][track_id].popleft()
    
    def _analysis_loop(self):
        """分析循环线程"""
        rate = rospy.Rate(self.config['publish_rate'])
        
        while self.analysis_active and not rospy.is_shutdown():
            try:
                self._analyze_spatial_info()
            except Exception as e:
                rospy.logwarn(f"空间分析错误: {e}")
            
            rate.sleep()
    
    def _analyze_spatial_info(self):
        """分析空间信息"""
        current_time = time.time()
        spatial_infos = []
        
        with self.buffer_lock:
            for cam_name, track_data in self.detection_buffer.items():
                for track_id, detections in track_data.items():
                    if len(detections) < self.config['min_detections']:
                        continue
                    
                    # 检查数据时效性
                    latest_time = max(det['timestamp'] for det in detections)
                    if current_time - latest_time > self.config['analysis_window']:
                        continue
                    
                    # 计算空间信息
                    try:
                        spatial_info = self._compute_spatial_info(cam_name, track_id, detections)
                        if spatial_info:
                            spatial_infos.append(spatial_info)
                    
                    except Exception as e:
                        rospy.logwarn(f"计算 {cam_name} TrackID{track_id} 空间信息失败: {e}")
        
        # 发布结果
        if spatial_infos:
            spatial_array = SpatialInfoArray()
            spatial_array.header = Header()
            spatial_array.header.stamp = rospy.Time.now()
            spatial_array.header.frame_id = "base_link"
            spatial_array.spatial_infos = spatial_infos
            
            self.spatial_pub.publish(spatial_array)
    
    def _compute_spatial_info(self, cam_name, track_id, detections):
        """计算单个目标的空间信息"""
        distances = []
        azimuths = []
        positions_2d = []
        confidences = []
        
        for det in detections:
            try:
                # 计算位置向量
                position_vector, distance, theta, azimuth, elevation = calculate_position_vector_planar(
                    det['width'], det['x_center'], det['y_center'], 
                    self.camera_matrix_inv, self.config['real_width']
                )
                
                # 过滤异常值
                if 0.1 < distance < self.config['max_distance']:
                    distances.append(distance)
                    azimuths.append(azimuth)
                    positions_2d.append([position_vector[0], position_vector[1]])
                    confidences.append(det['confidence'])
            
            except Exception as e:
                continue
        
        if not distances:
            return None
        
        # 计算平均值
        avg_distance = np.mean(distances)
        avg_azimuth = np.mean(azimuths)
        avg_position_2d = np.mean(positions_2d, axis=0)
        avg_confidence = np.mean(confidences)
        
        # 计算世界坐标系中的位置
        camera_orientation = self.camera_orientations.get(cam_name, 0)
        world_azimuth = (camera_orientation + avg_azimuth) % 360
        
        # 创建空间信息消息
        spatial_info = SpatialInfo()
        spatial_info.camera_name = cam_name
        spatial_info.track_id = track_id
        spatial_info.distance = avg_distance
        spatial_info.azimuth = avg_azimuth
        spatial_info.world_azimuth = world_azimuth
        spatial_info.position_x = avg_position_2d[0]
        spatial_info.position_y = avg_position_2d[1]
        spatial_info.confidence = avg_confidence
        spatial_info.detection_count = len(detections)
        spatial_info.timestamp = time.time()
        
        rospy.logdebug(f"📐 {cam_name} TrackID{track_id}: 距离={avg_distance:.2f}m, 方位={avg_azimuth:.1f}°, 世界方位={world_azimuth:.1f}°")
        
        return spatial_info
    
    def _filter_outliers(self, values, threshold=2.0):
        """过滤异常值"""
        if len(values) < 3:
            return values
        
        mean = np.mean(values)
        std = np.std(values)
        
        filtered = [v for v in values if abs(v - mean) <= threshold * std]
        return filtered if filtered else values
    
    def shutdown(self):
        """关闭节点"""
        self.analysis_active = False
        rospy.loginfo("🛑 空间分析节点关闭")

def main():
    try:
        node = SpatialAnalysisNode()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
    finally:
        if 'node' in locals():
            node.shutdown()

if __name__ == '__main__':
    main()
