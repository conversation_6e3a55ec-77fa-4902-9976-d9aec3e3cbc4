#!/usr/bin/env python3
"""
视觉检测节点 - 解耦版本
负责多摄像头目标检测，异步发布检测结果
"""

import rospy
import threading
import time
import os
import shutil
import numpy as np
from std_msgs.msg import Header
from sensor_msgs.msg import Image
from cv_bridge import CvBridge
from ultralytics import YOLO
from swarm_msgs.msg import DetectionResult, DetectionArray  # 自定义消息类型

class VisionDetectionNode:
    """视觉检测节点"""
    
    def __init__(self):
        rospy.init_node('vision_detection_node', anonymous=True)
        
        # 配置参数
        self.config = {
            'cameras': ['/cam0/image_raw', '/cam1/image_raw', '/cam2/image_raw', '/cam3/image_raw'],
            'model_path': '/home/<USER>/1206Cars-11/weights/best.engine',
            'detection_duration': 5.0,
            'early_stop_enabled': True,
            'early_stop_no_target_duration': 2.0,
            'min_detection_duration': 1.0,
            'publish_rate': 10.0  # Hz
        }
        
        # 初始化组件
        self.bridge = CvBridge()
        self.model = YOLO(self.config['model_path'])
        
        # 发布器
        self.detection_pub = rospy.Publisher('/detection_results', DetectionArray, queue_size=10)
        
        # 检测状态
        self.detection_active = False
        self.detection_results = {}
        self.detection_lock = threading.Lock()
        
        rospy.loginfo("🎥 视觉检测节点初始化完成")
    
    def start_detection(self):
        """启动异步检测"""
        if self.detection_active:
            rospy.logwarn("检测已在进行中")
            return
            
        self.detection_active = True
        self.detection_results = {}
        
        rospy.loginfo("🎥 启动多摄像头异步检测...")
        
        # 启动检测线程
        detection_threads = []
        for camera in self.config['cameras']:
            thread = threading.Thread(target=self._detect_camera, args=(camera,))
            thread.daemon = True
            thread.start()
            detection_threads.append(thread)
        
        # 启动结果发布线程
        publish_thread = threading.Thread(target=self._publish_results)
        publish_thread.daemon = True
        publish_thread.start()
        
        return detection_threads
    
    def _detect_camera(self, camera_topic):
        """单摄像头检测线程"""
        cam_name = camera_topic.split('/')[-2]
        rospy.loginfo(f"🎥 启动 {cam_name} 检测线程")
        
        start_time = time.time()
        last_target_time = start_time
        detection_stopped = False
        
        def image_callback(msg):
            nonlocal last_target_time, detection_stopped
            
            if not self.detection_active or detection_stopped:
                return
                
            current_time = time.time()
            
            # 检查超时
            if current_time - start_time > self.config['detection_duration']:
                detection_stopped = True
                return
            
            # 智能早停检查
            if self.config['early_stop_enabled']:
                if current_time - start_time >= self.config['min_detection_duration']:
                    no_target_duration = current_time - last_target_time
                    if no_target_duration >= self.config['early_stop_no_target_duration']:
                        detection_stopped = True
                        rospy.loginfo(f"⏹️ {cam_name}: 智能早停")
                        return
            
            # YOLO检测
            cv_image = self.bridge.imgmsg_to_cv2(msg, desired_encoding='bgr8')
            results = self.model.track(cv_image, imgsz=(640, 384), stream=True)
            
            detections = []
            has_valid_target = False
            
            for result in results:
                if result.boxes is not None and len(result.boxes) > 0:
                    boxes_data = result.boxes.xywh.cpu().numpy()
                    cls_data = result.boxes.cls.cpu().numpy() if result.boxes.cls is not None else None
                    id_data = result.boxes.id.cpu().numpy() if result.boxes.id is not None else None
                    conf_data = result.boxes.conf.cpu().numpy() if result.boxes.conf is not None else None
                    
                    for i, box_xywh in enumerate(boxes_data):
                        cls = int(cls_data[i]) if cls_data is not None else -1
                        track_id = int(id_data[i]) if id_data is not None else -1
                        confidence = float(conf_data[i]) if conf_data is not None else 0.0
                        x_center, y_center, width, height = box_xywh
                        
                        detection = {
                            'track_id': track_id,
                            'class_id': cls,
                            'x_center': float(x_center),
                            'y_center': float(y_center),
                            'width': float(width),
                            'height': float(height),
                            'confidence': confidence,
                            'timestamp': current_time
                        }
                        detections.append(detection)
                        
                        if cls != 110:  # 有效目标
                            has_valid_target = True
            
            # 更新检测结果
            with self.detection_lock:
                if cam_name not in self.detection_results:
                    self.detection_results[cam_name] = []
                self.detection_results[cam_name].extend(detections)
            
            if has_valid_target:
                last_target_time = current_time
        
        # 订阅摄像头话题
        sub = rospy.Subscriber(camera_topic, Image, image_callback)
        
        # 等待检测完成
        while self.detection_active and not detection_stopped and not rospy.is_shutdown():
            time.sleep(0.1)
        
        sub.unregister()
        rospy.loginfo(f"✅ {cam_name} 检测线程结束")
    
    def _publish_results(self):
        """发布检测结果线程"""
        rate = rospy.Rate(self.config['publish_rate'])
        
        while self.detection_active and not rospy.is_shutdown():
            with self.detection_lock:
                if self.detection_results:
                    # 创建检测结果消息
                    detection_array = DetectionArray()
                    detection_array.header = Header()
                    detection_array.header.stamp = rospy.Time.now()
                    detection_array.header.frame_id = "base_link"
                    
                    for cam_name, detections in self.detection_results.items():
                        for det in detections:
                            detection_msg = DetectionResult()
                            detection_msg.camera_name = cam_name
                            detection_msg.track_id = det['track_id']
                            detection_msg.class_id = det['class_id']
                            detection_msg.x_center = det['x_center']
                            detection_msg.y_center = det['y_center']
                            detection_msg.width = det['width']
                            detection_msg.height = det['height']
                            detection_msg.confidence = det['confidence']
                            detection_msg.timestamp = det['timestamp']
                            
                            detection_array.detections.append(detection_msg)
                    
                    # 发布结果
                    self.detection_pub.publish(detection_array)
                    
                    # 清空已发布的结果（可选，根据需求决定）
                    # self.detection_results = {}
            
            rate.sleep()
    
    def stop_detection(self):
        """停止检测"""
        self.detection_active = False
        rospy.loginfo("🛑 停止视觉检测")
    
    def run(self):
        """运行节点"""
        rospy.loginfo("🎥 视觉检测节点运行中...")
        
        # 可以添加服务接口来控制检测的启动和停止
        # 或者通过话题订阅控制指令
        
        try:
            rospy.spin()
        except KeyboardInterrupt:
            rospy.loginfo("🛑 视觉检测节点关闭")
        finally:
            self.stop_detection()

def main():
    try:
        node = VisionDetectionNode()
        node.run()
    except rospy.ROSInterruptException:
        pass

if __name__ == '__main__':
    main()
