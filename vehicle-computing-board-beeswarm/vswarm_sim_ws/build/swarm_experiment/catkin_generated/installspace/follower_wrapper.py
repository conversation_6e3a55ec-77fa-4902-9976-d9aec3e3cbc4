#!/usr/bin/env python3
"""
Follower自动化节点
直接实现2D空间检测功能，无需交互式菜单
基于重构版本的核心功能
"""

import sys
import os
import rospy
import socket
from geometry_msgs.msg import Twist

def warmup_detection(detector):
    """预热检测：解决第一次检测性能差的问题"""
    rospy.loginfo("🔥 开始检测系统预热...")

    try:
        # 进行一次短时间的预热检测，让YOLO模型加载完成
        rospy.loginfo("🤖 预热YOLO模型...")
        warmup_detections = detector.start_2d_detection()

        if warmup_detections:
            rospy.loginfo(f"✅ 预热检测成功，检测到{len(warmup_detections)}个摄像头数据")
        else:
            rospy.loginfo("✅ 预热检测完成，模型已加载")

        rospy.loginfo("✅ 检测系统预热完成，后续检测性能将正常")

    except Exception as e:
        rospy.logwarn(f"⚠️ 预热检测出错: {e}")
        rospy.loginfo("🔄 继续正常检测流程...")

def main():
    """主函数"""
    rospy.init_node('follower_behavior', anonymous=True)

    # 设置环境变量
    os.environ['HOSTNAME'] = socket.gethostname()

    # 获取配置模式参数
    config_mode = rospy.get_param('~config_mode', 'standard')  # standard/optimized

    rospy.loginfo("🚀 启动Follower自动化灯语识别系统...")
    rospy.loginfo(f"📍 当前节点: {os.environ['HOSTNAME']}")
    rospy.loginfo(f"⚙️ 配置模式: {config_mode}")

    # 使用本地复制的重构版本模块（重命名为swarm_experiment_core避免冲突）
    swarm_path = "/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm"

    # 检查本地模块路径是否存在
    swarm_module_path = os.path.join(swarm_path, "swarm_experiment_core")
    if not os.path.exists(swarm_module_path):
        rospy.logerr(f"❌ 本地重构版本模块路径不存在: {swarm_module_path}")
        return

    # 添加到Python路径
    if swarm_path not in sys.path:
        sys.path.insert(0, swarm_path)

    rospy.loginfo(f"✅ 使用本地重构版本模块: {swarm_module_path}")

    try:
        # 打印当前Python路径用于调试
        rospy.loginfo(f"🔍 当前Python路径: {sys.path[:3]}")

        # 逐个导入模块，便于定位问题
        rospy.loginfo("🔄 开始导入重构版本模块...")

        from swarm_experiment_core.core.config import SwarmConfig
        rospy.loginfo("✅ SwarmConfig导入成功")

        from swarm_experiment_core.detection.multi_target_detector import MultiTargetDetector
        rospy.loginfo("✅ MultiTargetDetector导入成功")

        from swarm_experiment_core.analysis.spatial_analyzer import SpatialAnalyzer
        rospy.loginfo("✅ SpatialAnalyzer导入成功")

        from swarm_experiment_core.analysis.flash_analyzer import FlashAnalyzer
        rospy.loginfo("✅ FlashAnalyzer导入成功")

        from swarm_experiment_core.behavior.swarm_behavior import SwarmBehavior
        rospy.loginfo("✅ SwarmBehavior导入成功")

        rospy.loginfo("✅ 所有重构版本模块导入成功")

        # 初始化配置
        config = SwarmConfig()

        # 应用优化配置（如果选择）
        if config_mode == 'optimized':
            try:
                # 添加当前包路径以导入优化配置
                current_pkg_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                sys.path.append(current_pkg_path)

                from config.optimized_detection_config import apply_optimized_config
                apply_optimized_config(config)
                rospy.loginfo("🚀 已应用优化检测配置 - 预期响应时间4-7秒")
            except ImportError as e:
                rospy.logwarn(f"⚠️ 无法加载优化配置，使用标准配置: {e}")
        else:
            rospy.loginfo("📊 使用标准检测配置 - 预期响应时间8-13秒")

        rospy.loginfo(f"🔧 配置初始化完成 - 节点: {config.node_name}")

        # 创建速度发布者
        vel_pub = rospy.Publisher('/cmd_vel', Twist, queue_size=10)
        rospy.loginfo("✅ 速度发布者创建成功")

        # 初始化各个模块
        detector = MultiTargetDetector(config)
        spatial_analyzer = SpatialAnalyzer(config)
        flash_analyzer = FlashAnalyzer(config)
        behavior_executor = SwarmBehavior(config, vel_pub)

        rospy.loginfo("🎯 开始2D空间检测（自动化模式）...")

        # 🔥 预热阶段：解决第一次检测性能差的问题
        warmup_detection(detector)

        # 启动检测循环（相当于原来的选项1功能）
        rospy.loginfo("🔄 开始连续检测循环...")

        detection_count = 0
        while not rospy.is_shutdown():
            try:
                detection_count += 1
                rospy.loginfo(f"🎥 开始第{detection_count}次检测...")

                # 1. 启动2D空间检测（这会进行5秒的检测）
                all_camera_detections = detector.start_2d_detection()

                if not all_camera_detections:
                    rospy.loginfo("⚠️ 未检测到任何目标，继续下一轮检测...")
                    continue

                rospy.loginfo(f"✅ 检测到{len(all_camera_detections)}个摄像头的数据")

                # 2. 灯语分析 - 找出有效指令的目标
                rospy.loginfo("🔍 开始灯语分析...")
                valid_targets = flash_analyzer.analyze_flash_patterns(all_camera_detections)

                if not valid_targets:
                    rospy.loginfo("⚠️ 未识别到有效灯语指令，继续下一轮检测...")
                    continue

                rospy.loginfo(f"✅ 识别到{len(valid_targets)}个有效灯语指令")

                # 3. 空间分析 - 计算目标空间信息
                rospy.loginfo("📐 开始空间分析...")
                candidates = spatial_analyzer.calculate_target_spatial_info(all_camera_detections, valid_targets)

                if not candidates:
                    rospy.loginfo("⚠️ 空间分析未找到合适候选目标，继续下一轮检测...")
                    continue

                rospy.loginfo(f"✅ 空间分析找到{len(candidates)}个候选目标")

                # 4. 选择最优目标并执行行为
                best_target = spatial_analyzer.select_best_candidate(candidates)

                if best_target:
                    command = best_target.get('command', 'unknown')
                    rospy.loginfo(f"🎯 执行最优目标行为: {command}")
                    behavior_executor.execute_behavior(best_target)
                else:
                    rospy.loginfo("⚠️ 未找到最优目标，继续下一轮检测...")

            except Exception as e:
                rospy.logwarn(f"⚠️ 检测循环中出错: {e}")
                import traceback
                traceback.print_exc()

    except ImportError as e:
        rospy.logerr(f"❌ 导入重构版本模块失败: {e}")
        rospy.logerr("请确保重构版本的swarm_experiment模块在正确路径")
        return

    except Exception as e:
        rospy.logerr(f"❌ Follower系统启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    try:
        main()
    except rospy.ROSInterruptException:
        rospy.loginfo("👋 Follower自动化节点退出")
    except Exception as e:
        rospy.logerr(f"❌ Follower节点错误: {e}")
