#!/usr/bin/env python3
"""
灯语分析节点 - 解耦版本
负责分析检测结果中的闪烁模式，异步发布灯语指令
"""

import rospy
import threading
import time
from collections import defaultdict, deque
from std_msgs.msg import Header
from swarm_msgs.msg import DetectionArray, FlashPattern, FlashPatternArray
from newan import analyze_flash_patterns_v5_multi_target, map_patterns_to_numbers_multi_target

class FlashAnalysisNode:
    """灯语分析节点"""
    
    def __init__(self):
        rospy.init_node('flash_analysis_node', anonymous=True)
        
        # 配置参数
        self.config = {
            'flash_num_frames': 36,
            'flash_start_frame': 7,
            'flash_threshold': 0.2,
            'analysis_window': 5.0,  # 分析窗口时间(秒)
            'min_detections': 10,    # 最小检测次数
            'publish_rate': 5.0      # Hz
        }
        
        # 群体指令映射
        self.SWARM_COMMANDS = {
            1: "approach",
            2: "follow", 
            3: "avoid",
            4: "circle",
            5: "stop",
            6: "align",
            7: "retreat",
            8: "parallel"
        }
        
        # 数据缓存
        self.detection_buffer = defaultdict(lambda: defaultdict(deque))  # cam_name -> track_id -> deque
        self.buffer_lock = threading.Lock()
        
        # 发布器和订阅器
        self.flash_pub = rospy.Publisher('/flash_patterns', FlashPatternArray, queue_size=10)
        self.detection_sub = rospy.Subscriber('/detection_results', DetectionArray, self._detection_callback)
        
        # 启动分析线程
        self.analysis_active = True
        self.analysis_thread = threading.Thread(target=self._analysis_loop)
        self.analysis_thread.daemon = True
        self.analysis_thread.start()
        
        rospy.loginfo("🔆 灯语分析节点初始化完成")
    
    def _detection_callback(self, msg):
        """检测结果回调"""
        current_time = time.time()
        
        with self.buffer_lock:
            for detection in msg.detections:
                cam_name = detection.camera_name
                track_id = detection.track_id
                
                # 只保留有效目标
                if detection.class_id != 110:
                    detection_data = {
                        'class_id': detection.class_id,
                        'x_center': detection.x_center,
                        'y_center': detection.y_center,
                        'width': detection.width,
                        'height': detection.height,
                        'confidence': detection.confidence,
                        'timestamp': current_time
                    }
                    
                    # 添加到缓存
                    self.detection_buffer[cam_name][track_id].append(detection_data)
                    
                    # 限制缓存大小
                    max_buffer_size = int(self.config['analysis_window'] * 30)  # 假设30fps
                    if len(self.detection_buffer[cam_name][track_id]) > max_buffer_size:
                        self.detection_buffer[cam_name][track_id].popleft()
    
    def _analysis_loop(self):
        """分析循环线程"""
        rate = rospy.Rate(self.config['publish_rate'])
        
        while self.analysis_active and not rospy.is_shutdown():
            try:
                self._analyze_flash_patterns()
            except Exception as e:
                rospy.logwarn(f"灯语分析错误: {e}")
            
            rate.sleep()
    
    def _analyze_flash_patterns(self):
        """分析闪烁模式"""
        current_time = time.time()
        flash_patterns = []
        
        with self.buffer_lock:
            for cam_name, track_data in self.detection_buffer.items():
                for track_id, detections in track_data.items():
                    if len(detections) < self.config['min_detections']:
                        continue
                    
                    # 检查数据时效性
                    latest_time = max(det['timestamp'] for det in detections)
                    if current_time - latest_time > self.config['analysis_window']:
                        continue
                    
                    # 准备分析数据
                    analysis_data = []
                    for i, det in enumerate(detections):
                        # 模拟label格式: track_id class_id x_center y_center width height
                        label_line = f"{track_id} {det['class_id']} {det['x_center']:.2f} {det['y_center']:.2f} {det['width']:.2f} {det['height']:.2f}"
                        analysis_data.append((i, [label_line]))
                    
                    # 执行闪烁模式分析
                    try:
                        patterns = self._analyze_single_target(analysis_data, track_id)
                        if patterns and patterns != ["无闪烁"]:
                            # 映射到数字指令
                            numbers = self._map_patterns_to_numbers(patterns)
                            
                            for pattern, number in zip(patterns, numbers):
                                if number != -1 and number in self.SWARM_COMMANDS:
                                    flash_pattern = FlashPattern()
                                    flash_pattern.camera_name = cam_name
                                    flash_pattern.track_id = track_id
                                    flash_pattern.pattern = pattern
                                    flash_pattern.command_id = number
                                    flash_pattern.command_name = self.SWARM_COMMANDS[number]
                                    flash_pattern.confidence = self._calculate_pattern_confidence(detections)
                                    flash_pattern.timestamp = current_time
                                    
                                    flash_patterns.append(flash_pattern)
                                    
                                    rospy.loginfo(f"🔆 检测到灯语: {cam_name} TrackID{track_id} → {pattern} → {self.SWARM_COMMANDS[number]}")
                    
                    except Exception as e:
                        rospy.logwarn(f"分析 {cam_name} TrackID{track_id} 失败: {e}")
        
        # 发布结果
        if flash_patterns:
            flash_array = FlashPatternArray()
            flash_array.header = Header()
            flash_array.header.stamp = rospy.Time.now()
            flash_array.header.frame_id = "base_link"
            flash_array.patterns = flash_patterns
            
            self.flash_pub.publish(flash_array)
    
    def _analyze_single_target(self, analysis_data, track_id):
        """分析单个目标的闪烁模式"""
        # 这里需要适配现有的分析函数
        # 由于原函数需要文件路径，我们需要创建临时数据结构
        
        # 构建类似于原函数期望的数据结构
        track_data = defaultdict(list)
        for frame_idx, label_lines in analysis_data:
            for line in label_lines:
                parts = line.strip().split()
                if len(parts) >= 6:
                    tid = int(parts[0])
                    cls = int(parts[1])
                    if tid == track_id:
                        track_data[frame_idx].append((cls, float(parts[2]), float(parts[3]), float(parts[4]), float(parts[5])))
        
        # 执行闪烁模式分析逻辑（简化版本）
        patterns = self._compute_flash_pattern(track_data, track_id)
        return patterns
    
    def _compute_flash_pattern(self, track_data, track_id):
        """计算闪烁模式（简化版本）"""
        if not track_data:
            return ["无闪烁"]
        
        # 统计每帧的类别分布
        frame_patterns = []
        for frame_idx in sorted(track_data.keys()):
            detections = track_data[frame_idx]
            if detections:
                # 统计0类和非0类的比例
                zero_class_count = sum(1 for det in detections if det[0] == 0)
                non_zero_class_count = len(detections) - zero_class_count
                
                if zero_class_count + non_zero_class_count > 0:
                    ratio = non_zero_class_count / (zero_class_count + non_zero_class_count)
                    
                    # 根据比例生成模式
                    if ratio > 0.8:
                        frame_patterns.append('1')
                    elif ratio > 0.6:
                        frame_patterns.append('2')
                    elif ratio > 0.4:
                        frame_patterns.append('3')
                    elif ratio > 0.2:
                        frame_patterns.append('4')
                    else:
                        frame_patterns.append('0')
        
        # 生成最终模式
        if len(frame_patterns) >= 3:
            pattern = ''.join(frame_patterns[:3])
            return [pattern]
        
        return ["无闪烁"]
    
    def _map_patterns_to_numbers(self, patterns):
        """映射模式到数字"""
        pattern_mapping = {
            '220': 1, '330': 2, '110': 3, '550': 4, '440': 5,
            '2200': 6, '3300': 7, '1100': 8, '5500': 9, '4400': 10
        }
        return [pattern_mapping.get(pattern, -1) for pattern in patterns]
    
    def _calculate_pattern_confidence(self, detections):
        """计算模式置信度"""
        if not detections:
            return 0.0
        
        # 基于检测次数和平均置信度计算
        avg_confidence = sum(det['confidence'] for det in detections) / len(detections)
        detection_count_factor = min(len(detections) / self.config['min_detections'], 1.0)
        
        return avg_confidence * detection_count_factor
    
    def shutdown(self):
        """关闭节点"""
        self.analysis_active = False
        rospy.loginfo("🛑 灯语分析节点关闭")

def main():
    try:
        node = FlashAnalysisNode()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
    finally:
        if 'node' in locals():
            node.shutdown()

if __name__ == '__main__':
    main()
