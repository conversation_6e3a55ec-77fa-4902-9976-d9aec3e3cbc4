set(swarm_experiment_MESSAGE_FILES "/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/src/swarm_experiment/msg/DetectionResult.msg;/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/src/swarm_experiment/msg/DetectionArray.msg;/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/src/swarm_experiment/msg/FlashPattern.msg;/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/src/swarm_experiment/msg/FlashPatternArray.msg;/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/src/swarm_experiment/msg/SpatialInfo.msg;/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/src/swarm_experiment/msg/SpatialInfoArray.msg;/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/src/swarm_experiment/msg/SwarmCommand.msg;/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/src/swarm_experiment/msg/PredictedState.msg;/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/src/swarm_experiment/msg/PredictedStateArray.msg;/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/src/swarm_experiment/msg/ExecutionFeedback.msg")
set(swarm_experiment_SERVICE_FILES "/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/src/swarm_experiment/srv/SetLight.srv")
