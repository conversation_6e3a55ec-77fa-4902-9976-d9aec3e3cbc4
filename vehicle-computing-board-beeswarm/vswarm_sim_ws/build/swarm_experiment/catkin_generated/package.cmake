set(_CATKIN_CURRENT_PACKAGE "swarm_experiment")
set(swarm_experiment_VERSION "1.0.0")
set(swarm_experiment_MAINTAINER "BeeSwarm Team <<EMAIL>>")
set(swarm_experiment_PACKAGE_FORMAT "2")
set(swarm_experiment_BUILD_DEPENDS "message_generation" "rospy" "std_msgs" "std_srvs" "geometry_msgs" "sensor_msgs" "gazebo_msgs")
set(swarm_experiment_BUILD_EXPORT_DEPENDS "rospy" "std_msgs" "std_srvs" "geometry_msgs" "sensor_msgs" "gazebo_msgs")
set(swarm_experiment_BUILDTOOL_DEPENDS "catkin")
set(swarm_experiment_BUILDTOOL_EXPORT_DEPENDS )
set(swarm_experiment_EXEC_DEPENDS "message_runtime" "rospy" "std_msgs" "std_srvs" "geometry_msgs" "sensor_msgs" "gazebo_msgs")
set(swarm_experiment_RUN_DEPENDS "message_runtime" "rospy" "std_msgs" "std_srvs" "geometry_msgs" "sensor_msgs" "gazebo_msgs")
set(swarm_experiment_TEST_DEPENDS )
set(swarm_experiment_DOC_DEPENDS )
set(swarm_experiment_URL_WEBSITE "")
set(swarm_experiment_URL_BUGTRACKER "")
set(swarm_experiment_URL_REPOSITORY "")
set(swarm_experiment_DEPRECATED "")