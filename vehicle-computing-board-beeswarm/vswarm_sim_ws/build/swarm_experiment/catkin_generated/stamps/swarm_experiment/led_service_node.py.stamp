#!/usr/bin/env python3
"""
LED服务节点
扩展现有interactive_led_control.py，提供服务接口
保持现有协议不变，添加程序化调用能力
"""

import rospy
import socket
import time
from std_msgs.msg import Int32
from std_srvs.srv import SetBool, SetBoolResponse
from swarm_experiment.srv import SetLight, SetLightResponse

class LEDServiceNode:
    """LED控制服务节点"""
    
    def __init__(self):
        rospy.init_node('led_service_node', anonymous=True)
        
        # 获取当前节点名称
        self.node_name = socket.gethostname()
        
        # LED模式定义（与现有系统保持一致）
        self.modes = {
            0: ("常亮模式", "淡蓝色", "不闪烁"),
            1: ("慢闪烁", "红色", "5Hz"),
            2: ("慢闪烁", "绿色", "5Hz"),
            3: ("慢闪烁", "蓝色", "5Hz"),
            4: ("慢闪烁", "黄色", "5Hz"),
            5: ("慢闪烁", "紫色", "5Hz"),
            6: ("快闪烁", "红色", "10Hz"),
            7: ("快闪烁", "绿色", "10Hz"),
            8: ("快闪烁", "蓝色", "10Hz"),
            9: ("快闪烁", "黄色", "10Hz"),
            10: ("快闪烁", "紫色", "10Hz"),
        }
        
        # 语义到模式的映射
        self.semantic_modes = {
            "launch": 0,      # 白常亮
            "gather": 3,      # 蓝单闪
            "follow": 7,      # 绿快闪
            "disperse": 4,    # 黄单闪
            "finish": 5,      # 紫单闪
            "stop": 1,        # 红单闪
            "emergency": 6,   # 红快闪
        }
        
        self.current_mode = 0
        
        # 创建发布者（与现有系统兼容）
        self.led_pub = rospy.Publisher(f'/{self.node_name}/led_mode', Int32, queue_size=10)
        
        # 创建服务
        self.setup_services()
        
        # 等待发布者初始化
        time.sleep(1)
        
        rospy.loginfo(f"🔆 LED服务节点启动 - 节点: {self.node_name}")
        rospy.loginfo(f"📡 发布话题: /{self.node_name}/led_mode")
        
    def setup_services(self):
        """设置服务"""
        # 语义LED控制服务
        self.set_light_srv = rospy.Service(
            f'/{self.node_name}/set_light', 
            SetLight, 
            self.handle_set_light
        )
        
        # 直接模式控制服务
        self.set_mode_srv = rospy.Service(
            f'/{self.node_name}/set_led_mode',
            SetBool,  # 临时使用，实际应该定义专门的srv
            self.handle_set_mode
        )
        
        rospy.loginfo("✅ LED控制服务已启动")
        rospy.loginfo(f"   - /{self.node_name}/set_light")
        rospy.loginfo(f"   - /{self.node_name}/set_led_mode")
    
    def handle_set_light(self, req):
        """处理语义LED设置请求"""
        response = SetLightResponse()
        
        try:
            semantic = req.light_semantic.lower()
            duration = req.duration
            
            if semantic in self.semantic_modes:
                mode = self.semantic_modes[semantic]
                success = self.set_led_mode(mode)
                
                if success:
                    response.success = True
                    response.message = f"设置灯语: {semantic} → 模式{mode}"
                    
                    # 如果指定了持续时间，启动定时器
                    if duration > 0:
                        rospy.Timer(
                            rospy.Duration(duration), 
                            lambda event: self.set_led_mode(0),  # 回到常亮
                            oneshot=True
                        )
                        response.message += f" (持续{duration}s)"
                else:
                    response.success = False
                    response.message = f"设置LED模式失败"
            else:
                response.success = False
                response.message = f"未知灯语语义: {semantic}"
                rospy.logwarn(f"⚠️ 未知灯语语义: {semantic}")
                
        except Exception as e:
            response.success = False
            response.message = f"服务处理错误: {str(e)}"
            rospy.logerr(f"❌ LED服务错误: {e}")
        
        return response
    
    def handle_set_mode(self, req):
        """处理直接模式设置请求（临时实现）"""
        response = SetBoolResponse()
        
        try:
            # 这里应该从req中获取模式号，但SetBool服务没有这个字段
            # 实际使用时应该定义专门的服务类型
            rospy.logwarn("⚠️ 直接模式设置服务需要专门的srv定义")
            response.success = False
            response.message = "需要使用set_light服务或定义专门的srv类型"
            
        except Exception as e:
            response.success = False
            response.message = f"服务处理错误: {str(e)}"
            
        return response
    
    def set_led_mode(self, mode):
        """设置LED模式（核心功能，与现有系统兼容）"""
        if mode in self.modes:
            msg = Int32()
            msg.data = mode
            self.led_pub.publish(msg)
            self.current_mode = mode
            
            mode_type, color, frequency = self.modes[mode]
            rospy.loginfo(f"✓ 设置 {self.node_name} LED模式 {mode}: {mode_type} - {color} - {frequency}")
            return True
        else:
            rospy.logwarn(f"✗ 无效的模式: {mode}")
            return False
    
    def get_available_semantics(self):
        """获取可用的语义列表"""
        return list(self.semantic_modes.keys())
    
    def get_current_mode(self):
        """获取当前模式"""
        return self.current_mode
    
    def run(self):
        """运行服务节点"""
        rospy.loginfo("🎯 LED服务节点就绪，等待服务调用...")
        rospy.loginfo(f"📋 可用语义: {', '.join(self.get_available_semantics())}")
        
        # 初始化为常亮模式
        self.set_led_mode(0)
        
        # 保持节点运行
        rospy.spin()

if __name__ == '__main__':
    try:
        node = LEDServiceNode()
        node.run()
    except rospy.ROSInterruptException:
        rospy.loginfo("👋 LED服务节点退出")
    except Exception as e:
        rospy.logerr(f"❌ LED服务节点错误: {e}")
