#!/usr/bin/env python3
"""
实验监控节点
监控实验进度和状态
"""

import rospy
import yaml
from std_msgs.msg import String

class ExperimentMonitor:
    """实验监控器"""
    
    def __init__(self):
        rospy.init_node('experiment_monitor', anonymous=True)
        
        # 获取参数
        self.profile = rospy.get_param('~profile', '1L2F')
        self.timeline_config = rospy.get_param('~timeline_config', '')
        
        # 加载时间线配置
        self.load_timeline_config()
        
        # 设置订阅者
        self.setup_subscribers()
        
        # 当前状态
        self.current_phase = "unknown"
        
        rospy.loginfo(f"📊 实验监控器启动 - 配置: {self.profile}")
        
    def load_timeline_config(self):
        """加载时间线配置"""
        if self.timeline_config:
            try:
                with open(self.timeline_config, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                    self.timeline = config.get('timeline', [])
                    rospy.loginfo(f"✅ 时间线配置加载成功，共{len(self.timeline)}个阶段")
            except Exception as e:
                rospy.logwarn(f"⚠️ 时间线配置加载失败: {e}")
                self.timeline = []
        else:
            self.timeline = []
    
    def setup_subscribers(self):
        """设置订阅者"""
        rospy.Subscriber('/experiment/phase', String, self.phase_callback)
    
    def phase_callback(self, msg):
        """实验阶段回调"""
        new_phase = msg.data
        if new_phase != self.current_phase:
            self.current_phase = new_phase
            rospy.loginfo(f"📍 实验阶段切换: {new_phase}")
            
            # 查找阶段信息
            phase_info = None
            for task in self.timeline:
                if task['name'] == new_phase:
                    phase_info = task
                    break
            
            if phase_info:
                rospy.loginfo(f"   ⏱️  开始时间: {phase_info['t_start']}s")
                rospy.loginfo(f"   ⏳ 持续时间: {phase_info['duration']}s")
                rospy.loginfo(f"   🔆 灯语: {phase_info['light']}")
                rospy.loginfo(f"   🚗 运动: {phase_info['motion']['type']}")
    
    def run(self):
        """运行监控器"""
        rospy.loginfo("🎯 实验监控器就绪")
        rospy.spin()

if __name__ == '__main__':
    try:
        monitor = ExperimentMonitor()
        monitor.run()
    except rospy.ROSInterruptException:
        rospy.loginfo("👋 实验监控器退出")
    except Exception as e:
        rospy.logerr(f"❌ 实验监控器错误: {e}")
