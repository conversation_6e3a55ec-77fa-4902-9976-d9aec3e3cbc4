#!/usr/bin/env python3
"""
Leader自动行为节点
按时间线脚本自动驱动Leader完成聚集→跟随→分散→收尾
"""

import rospy
import yaml
import math
import time
import socket
from std_msgs.msg import Int32, String
from geometry_msgs.msg import Twist
from gazebo_msgs.srv import GetModelState
from swarm_experiment.srv import SetLight
import tf.transformations as tf_trans

class LeaderBehaviorNode:
    """Leader自动行为控制器"""
    
    def __init__(self):
        rospy.init_node('leader_behavior_node', anonymous=True)
        self.node_name = socket.gethostname()
        
        # 获取参数
        self.mode = rospy.get_param('~mode', 'auto')  # auto/manual
        self.config_path = rospy.get_param('~config_path', 
            '/home/<USER>/BeeSwarm/Code/ros/swarm_experiment/config/task_timeline.yaml')
        
        # 加载配置
        self.load_timeline_config()
        
        # 初始化发布者
        self.setup_publishers()

        # 初始化Gazebo服务
        self.setup_gazebo_services()

        # 初始化LED服务客户端
        self.setup_led_service()
        
        # 状态变量
        self.current_task_index = 0
        self.start_time = None
        self.current_task = None
        self.guardrail_active = False
        self.guardrail_start_time = None
        
        # 运动控制状态
        self.current_pose = {'x': 0.0, 'y': 0.0, 'heading': 0.0}
        self.target_reached = False
        
        rospy.loginfo(f"🚗 Leader行为节点启动 - 模式: {self.mode}")
        rospy.loginfo(f"📋 加载配置: {self.config_path}")
        
    def load_timeline_config(self):
        """加载时间线配置"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)
            
            self.timeline = self.config['timeline']
            self.light_modes = self.config['light_modes']
            self.leader_config = self.config['leader']
            
            rospy.loginfo(f"✅ 配置加载成功，共{len(self.timeline)}个任务阶段")
            for i, task in enumerate(self.timeline):
                rospy.loginfo(f"   {i}: {task['name']} (t={task['t_start']}s, 持续{task['duration']}s)")
                
        except Exception as e:
            rospy.logerr(f"❌ 配置加载失败: {e}")
            raise
    
    def setup_publishers(self):
        """设置发布者"""
        # LED控制发布者
        self.led_pub = rospy.Publisher(f'/{self.node_name}/led_mode', Int32, queue_size=10)
        
        # 速度控制发布者
        self.vel_pub = rospy.Publisher(f'/{self.node_name}/vel_cmd', Twist, queue_size=10)

        # 实验阶段发布者
        self.phase_pub = rospy.Publisher('/experiment/phase', String, queue_size=10)

        # 等待发布者初始化
        time.sleep(1.0)
        
    def setup_gazebo_services(self):
        """设置Gazebo服务"""
        try:
            rospy.wait_for_service('/gazebo/get_model_state', timeout=5.0)
            self.get_model_state = rospy.ServiceProxy('/gazebo/get_model_state', GetModelState)
            rospy.loginfo("✅ Gazebo服务连接成功")
        except Exception as e:
            rospy.logwarn(f"⚠️ Gazebo服务连接失败: {e}")
            self.get_model_state = None

    def setup_led_service(self):
        """设置LED服务客户端"""
        try:
            service_name = f'/{self.node_name}/set_light'
            rospy.wait_for_service(service_name, timeout=5.0)
            self.led_service = rospy.ServiceProxy(service_name, SetLight)
            rospy.loginfo("✅ LED服务连接成功")
        except Exception as e:
            rospy.logwarn(f"⚠️ LED服务连接失败，使用直接发布: {e}")
            self.led_service = None
    
    def get_current_pose(self):
        """获取当前位置"""
        if self.get_model_state is None:
            return self.current_pose
            
        try:
            response = self.get_model_state(self.node_name, "world")
            if response.success:
                pos = response.pose.position
                ori = response.pose.orientation
                
                # 转换四元数到欧拉角
                euler = tf_trans.euler_from_quaternion([ori.x, ori.y, ori.z, ori.w])
                heading_rad = euler[2]  # yaw角
                heading_deg = math.degrees(heading_rad) % 360
                
                self.current_pose = {
                    'x': pos.x,
                    'y': pos.y, 
                    'heading': heading_deg
                }
                
        except Exception as e:
            rospy.logwarn(f"⚠️ 获取位置失败: {e}")
            
        return self.current_pose
    
    def set_led_mode(self, light_semantic):
        """设置LED模式"""
        try:
            # 优先使用LED服务
            if self.led_service is not None:
                response = self.led_service(light_semantic, 0.0)  # 持续到下次切换
                if response.success:
                    rospy.loginfo(f"🔆 通过服务设置灯语: {light_semantic}")
                else:
                    rospy.logwarn(f"⚠️ LED服务调用失败: {response.message}")
                    self.fallback_led_publish(light_semantic)
            else:
                self.fallback_led_publish(light_semantic)

            # 激活护栏
            if self.leader_config['guardrail']['enabled']:
                self.activate_guardrail()

        except Exception as e:
            rospy.logwarn(f"⚠️ LED设置失败: {e}")
            self.fallback_led_publish(light_semantic)

    def fallback_led_publish(self, light_semantic):
        """备用LED发布方法"""
        if light_semantic in self.light_modes:
            mode = self.light_modes[light_semantic]
            msg = Int32()
            msg.data = mode
            self.led_pub.publish(msg)
            rospy.loginfo(f"🔆 直接发布灯语: {light_semantic} → 模式{mode}")
        else:
            rospy.logwarn(f"⚠️ 未知灯语语义: {light_semantic}")
    
    def activate_guardrail(self):
        """激活护栏（灯语切换时的速度限制）"""
        self.guardrail_active = True
        self.guardrail_start_time = time.time()
        duration = self.leader_config['guardrail']['duration_s']
        rospy.loginfo(f"🛡️ 护栏激活 {duration}s")
    
    def apply_speed_limits(self, twist):
        """应用速度限制"""
        # 护栏限制（优先级最高）
        if self.guardrail_active:
            elapsed = time.time() - self.guardrail_start_time
            if elapsed < self.leader_config['guardrail']['duration_s']:
                speed_cap = self.leader_config['guardrail']['speed_cap']
                # 限制线速度
                linear_speed = math.sqrt(twist.linear.x**2 + twist.linear.y**2)
                if linear_speed > speed_cap:
                    scale = speed_cap / linear_speed
                    twist.linear.x *= scale
                    twist.linear.y *= scale
            else:
                self.guardrail_active = False
                rospy.loginfo("🛡️ 护栏解除")
        
        # 常规速度限制
        v_max = self.leader_config['speed_limits']['v_max']
        w_max = self.leader_config['speed_limits']['w_max']
        
        # 限制线速度
        linear_speed = math.sqrt(twist.linear.x**2 + twist.linear.y**2)
        if linear_speed > v_max:
            scale = v_max / linear_speed
            twist.linear.x *= scale
            twist.linear.y *= scale
        
        # 限制角速度
        if abs(twist.angular.z) > w_max:
            twist.angular.z = math.copysign(w_max, twist.angular.z)
        
        return twist
    
    def generate_motion(self, motion_config):
        """生成运动控制指令"""
        twist = Twist()
        
        if motion_config['type'] == 'hold':
            # 定点保持
            hold_v = motion_config.get('hold_v', 0.0)
            twist.linear.x = hold_v
            
        elif motion_config['type'] == 'segment':
            # 直线运动到目标点
            target = motion_config['target']
            v_ref = motion_config.get('v_ref', 0.2)
            
            current_pose = self.get_current_pose()
            
            # 计算到目标的距离和方向
            dx = target['x'] - current_pose['x']
            dy = target['y'] - current_pose['y']
            distance = math.sqrt(dx**2 + dy**2)
            
            # 到达判据
            if distance < 0.2:
                self.target_reached = True
                rospy.loginfo(f"✅ 到达目标点 ({target['x']:.1f}, {target['y']:.1f})")
                return twist
            
            # 简单P控制
            target_angle = math.degrees(math.atan2(dy, dx))
            angle_error = self.normalize_angle(target_angle - current_pose['heading'])
            
            # 全向移动控制
            if abs(angle_error) < 30:  # 朝向基本正确，前进
                speed = min(v_ref, distance * 0.5)  # 距离越近速度越慢
                twist.linear.x = speed * math.cos(math.radians(angle_error))
                twist.linear.y = speed * math.sin(math.radians(angle_error))
            else:  # 需要转向
                twist.angular.z = 0.3 * math.copysign(1, angle_error)
                
        elif motion_config['type'] == 'loiter':
            # 小范围巡航
            center = motion_config['center']
            radius = motion_config.get('radius', 0.5)
            v_ref = motion_config.get('v_ref', 0.15)
            
            current_pose = self.get_current_pose()
            
            # 计算到中心的距离
            dx = center['x'] - current_pose['x']
            dy = center['y'] - current_pose['y']
            distance_to_center = math.sqrt(dx**2 + dy**2)
            
            if distance_to_center > radius:
                # 回到巡航区域
                angle_to_center = math.atan2(dy, dx)
                twist.linear.x = v_ref * math.cos(angle_to_center)
                twist.linear.y = v_ref * math.sin(angle_to_center)
            else:
                # 在区域内缓慢移动
                twist.linear.x = v_ref * 0.5
                twist.angular.z = 0.2
        
        return self.apply_speed_limits(twist)
    
    def normalize_angle(self, angle):
        """角度归一化到[-180, 180]"""
        while angle > 180:
            angle -= 360
        while angle < -180:
            angle += 360
        return angle
    
    def run_timeline(self):
        """运行时间线"""
        if self.mode != 'auto':
            rospy.loginfo("⏸️ 手动模式，不执行自动时间线")
            return
            
        self.start_time = time.time()
        rospy.loginfo(f"🚀 开始执行时间线，共{len(self.timeline)}个阶段")
        
        rate = rospy.Rate(20)  # 20Hz控制频率
        
        while not rospy.is_shutdown():
            current_time = time.time() - self.start_time
            
            # 检查是否需要切换任务
            next_task = self.get_current_task(current_time)
            if next_task != self.current_task:
                self.switch_task(next_task, current_time)
            
            # 执行当前任务
            if self.current_task:
                self.execute_current_task(current_time)
            
            rate.sleep()
    
    def get_current_task(self, current_time):
        """获取当前应该执行的任务"""
        for task in self.timeline:
            task_start = task['t_start']
            task_end = task_start + task['duration']
            if task_start <= current_time < task_end:
                return task
        return None
    
    def switch_task(self, new_task, current_time):
        """切换任务"""
        if new_task is None:
            if self.current_task:
                rospy.loginfo(f"✅ 时间线执行完成")
                self.current_task = None
            return
            
        self.current_task = new_task
        self.target_reached = False
        
        rospy.loginfo(f"🔄 切换任务: {new_task['name']} (t={current_time:.1f}s)")
        rospy.loginfo(f"   灯语: {new_task['light']}")
        rospy.loginfo(f"   运动: {new_task['motion']['type']}")

        # 发布实验阶段信息
        phase_msg = String()
        phase_msg.data = new_task['name']
        self.phase_pub.publish(phase_msg)

        # 设置灯语
        self.set_led_mode(new_task['light'])
    
    def execute_current_task(self, current_time):
        """执行当前任务"""
        if not self.current_task:
            return

        # 生成运动控制
        twist = self.generate_motion(self.current_task['motion'])
        self.vel_pub.publish(twist)

        # 可以在这里添加基于时间的任务状态检查
        # 例如：检查任务是否应该提前结束等
    
    def warmup_phase(self):
        """预热阶段：解决第一次同步问题"""
        rospy.loginfo("🔥 开始系统预热阶段...")

        # 发布预热灯光信号（launch：白常亮）
        rospy.loginfo("💡 发布预热灯光信号...")
        self.set_led_mode("launch")  # 白常亮
        rospy.sleep(1.0)

        # 再次发布确保同步
        self.set_led_mode("launch")
        rospy.sleep(1.0)

        rospy.loginfo("✅ 系统预热完成，分布式灯光已同步")

    def run(self):
        """主运行函数"""
        rospy.loginfo("🎯 Leader行为节点就绪")

        if self.mode == 'auto':
            # 等待一下让系统稳定
            rospy.sleep(2.0)

            # 🔧 预热阶段：解决第一次同步问题
            self.warmup_phase()

            self.run_timeline()
        else:
            rospy.loginfo("📱 手动模式，等待外部控制...")
            rospy.spin()

if __name__ == '__main__':
    try:
        node = LeaderBehaviorNode()
        node.run()
    except rospy.ROSInterruptException:
        rospy.loginfo("👋 Leader行为节点退出")
    except Exception as e:
        rospy.logerr(f"❌ Leader行为节点错误: {e}")
