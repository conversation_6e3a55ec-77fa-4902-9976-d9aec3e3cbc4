#!/usr/bin/env python3
"""
CSV日志记录节点
记录实验数据用于验收分析
"""

import rospy
import csv
import os
import time
import math
import socket
from datetime import datetime
from std_msgs.msg import Int32, String
from geometry_msgs.msg import Twist
from gazebo_msgs.msg import ModelStates
from gazebo_msgs.srv import GetModelState

class CSVLoggerNode:
    """CSV日志记录器"""
    
    def __init__(self):
        rospy.init_node('csv_logger_node', anonymous=True)
        
        # 获取参数
        self.profile = rospy.get_param('~profile', '1L2F')
        self.output_dir = rospy.get_param('~output_dir', '/tmp/swarm_experiment_logs')
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.csv_filename = os.path.join(
            self.output_dir, 
            f"swarm_experiment_{self.profile}_{timestamp}.csv"
        )
        
        # 初始化数据存储
        self.setup_data_structures()
        
        # 设置订阅者
        self.setup_subscribers()
        
        # 设置Gazebo服务
        self.setup_gazebo_services()
        
        # 创建CSV文件并写入表头
        self.setup_csv_file()
        
        # 启动定时记录
        self.log_timer = rospy.Timer(rospy.Duration(0.2), self.log_data_callback)  # 5Hz
        
        rospy.loginfo(f"📊 CSV日志记录器启动")
        rospy.loginfo(f"📁 输出文件: {self.csv_filename}")
        rospy.loginfo(f"🔧 配置: {self.profile}")
        
    def setup_data_structures(self):
        """设置数据结构"""
        # 根据配置确定节点列表
        if self.profile == '1L2F':
            self.leader_nodes = ['leader1']
            self.follower_nodes = ['follower1', 'follower2']
        elif self.profile == '1L4F':
            self.leader_nodes = ['leader1']
            self.follower_nodes = ['follower1', 'follower2', 'follower3', 'follower4']
        else:
            # 默认配置，尝试自动检测
            self.leader_nodes = [socket.gethostname()]  # 假设当前节点是leader
            self.follower_nodes = []
        
        # 数据存储
        self.node_data = {}
        for node in self.leader_nodes + self.follower_nodes:
            self.node_data[node] = {
                'pose': {'x': 0.0, 'y': 0.0, 'heading': 0.0},
                'led_mode': 0,
                'velocity': {'vx': 0.0, 'vy': 0.0, 'vz': 0.0, 'wx': 0.0, 'wy': 0.0, 'wz': 0.0}
            }
        
        # 实验状态
        self.current_phase = "unknown"
        self.experiment_start_time = time.time()
        
    def setup_subscribers(self):
        """设置订阅者"""
        # 订阅所有节点的LED模式
        for node in self.leader_nodes + self.follower_nodes:
            rospy.Subscriber(
                f'/{node}/led_mode', 
                Int32, 
                lambda msg, node=node: self.led_mode_callback(msg, node)
            )
            
            # 订阅速度指令
            rospy.Subscriber(
                f'/{node}/vel_cmd', 
                Twist, 
                lambda msg, node=node: self.velocity_callback(msg, node)
            )
            rospy.Subscriber(
                f'/{node}/cmd_vel', 
                Twist, 
                lambda msg, node=node: self.velocity_callback(msg, node)
            )
        
        # 订阅实验阶段信息（如果有的话）
        rospy.Subscriber('/experiment/phase', String, self.phase_callback)
        
        # 订阅模型状态（Gazebo）
        rospy.Subscriber('/gazebo/model_states', ModelStates, self.model_states_callback)
        
    def setup_gazebo_services(self):
        """设置Gazebo服务"""
        try:
            rospy.wait_for_service('/gazebo/get_model_state', timeout=5.0)
            self.get_model_state = rospy.ServiceProxy('/gazebo/get_model_state', GetModelState)
            rospy.loginfo("✅ Gazebo服务连接成功")
        except Exception as e:
            rospy.logwarn(f"⚠️ Gazebo服务连接失败: {e}")
            self.get_model_state = None
    
    def setup_csv_file(self):
        """设置CSV文件和表头"""
        self.csv_file = open(self.csv_filename, 'w', newline='', encoding='utf-8')
        self.csv_writer = csv.writer(self.csv_file)
        
        # 构建表头
        headers = [
            'timestamp', 'elapsed_time', 'phase', 'leader_led_mode'
        ]
        
        # Leader位置和速度
        for leader in self.leader_nodes:
            headers.extend([
                f'{leader}_x', f'{leader}_y', f'{leader}_heading',
                f'{leader}_vx', f'{leader}_vy', f'{leader}_wz'
            ])
        
        # Follower位置、速度和相对信息
        for follower in self.follower_nodes:
            headers.extend([
                f'{follower}_x', f'{follower}_y', f'{follower}_heading',
                f'{follower}_vx', f'{follower}_vy', f'{follower}_wz',
                f'{follower}_distance_to_leader', f'{follower}_azimuth_to_leader'
            ])
        
        # 集群统计信息
        headers.extend([
            'swarm_centroid_x', 'swarm_centroid_y',
            'swarm_variance', 'avg_nearest_neighbor_distance',
            'leader_follower_avg_distance'
        ])
        
        self.csv_writer.writerow(headers)
        self.csv_file.flush()
        
        rospy.loginfo(f"📋 CSV表头已写入，共{len(headers)}列")
    
    def led_mode_callback(self, msg, node_name):
        """LED模式回调"""
        if node_name in self.node_data:
            self.node_data[node_name]['led_mode'] = msg.data
    
    def velocity_callback(self, msg, node_name):
        """速度回调"""
        if node_name in self.node_data:
            self.node_data[node_name]['velocity'] = {
                'vx': msg.linear.x,
                'vy': msg.linear.y,
                'vz': msg.linear.z,
                'wx': msg.angular.x,
                'wy': msg.angular.y,
                'wz': msg.angular.z
            }
    
    def phase_callback(self, msg):
        """实验阶段回调"""
        self.current_phase = msg.data
    
    def model_states_callback(self, msg):
        """模型状态回调"""
        for i, name in enumerate(msg.name):
            if name in self.node_data:
                pose = msg.pose[i]
                
                # 转换四元数到欧拉角
                import tf.transformations as tf_trans
                euler = tf_trans.euler_from_quaternion([
                    pose.orientation.x, pose.orientation.y, 
                    pose.orientation.z, pose.orientation.w
                ])
                heading_deg = math.degrees(euler[2]) % 360
                
                self.node_data[name]['pose'] = {
                    'x': pose.position.x,
                    'y': pose.position.y,
                    'heading': heading_deg
                }
    
    def calculate_relative_info(self, follower_pose, leader_pose):
        """计算相对位置信息"""
        dx = follower_pose['x'] - leader_pose['x']
        dy = follower_pose['y'] - leader_pose['y']
        
        distance = math.sqrt(dx**2 + dy**2)
        azimuth = math.degrees(math.atan2(dy, dx)) % 360
        
        return distance, azimuth
    
    def calculate_swarm_statistics(self):
        """计算集群统计信息"""
        all_poses = []
        for node in self.leader_nodes + self.follower_nodes:
            pose = self.node_data[node]['pose']
            all_poses.append([pose['x'], pose['y']])
        
        if not all_poses:
            return 0.0, 0.0, 0.0, 0.0, 0.0
        
        import numpy as np
        poses_array = np.array(all_poses)
        
        # 质心
        centroid = np.mean(poses_array, axis=0)
        
        # 方差（群心方差）
        variance = np.mean(np.sum((poses_array - centroid)**2, axis=1))
        
        # 平均最近邻距离
        distances = []
        for i in range(len(poses_array)):
            min_dist = float('inf')
            for j in range(len(poses_array)):
                if i != j:
                    dist = np.linalg.norm(poses_array[i] - poses_array[j])
                    min_dist = min(min_dist, dist)
            if min_dist != float('inf'):
                distances.append(min_dist)
        
        avg_nearest_neighbor = np.mean(distances) if distances else 0.0
        
        # Leader-Follower平均距离
        leader_follower_distances = []
        if self.leader_nodes and self.follower_nodes:
            leader_pose = self.node_data[self.leader_nodes[0]]['pose']
            for follower in self.follower_nodes:
                follower_pose = self.node_data[follower]['pose']
                distance, _ = self.calculate_relative_info(follower_pose, leader_pose)
                leader_follower_distances.append(distance)
        
        avg_leader_follower_dist = np.mean(leader_follower_distances) if leader_follower_distances else 0.0
        
        return centroid[0], centroid[1], variance, avg_nearest_neighbor, avg_leader_follower_dist
    
    def log_data_callback(self, event):
        """定时记录数据"""
        current_time = time.time()
        elapsed_time = current_time - self.experiment_start_time
        
        # 构建数据行
        row = [
            current_time,
            elapsed_time,
            self.current_phase,
            self.node_data[self.leader_nodes[0]]['led_mode'] if self.leader_nodes else 0
        ]
        
        # Leader数据
        for leader in self.leader_nodes:
            pose = self.node_data[leader]['pose']
            vel = self.node_data[leader]['velocity']
            row.extend([
                pose['x'], pose['y'], pose['heading'],
                vel['vx'], vel['vy'], vel['wz']
            ])
        
        # Follower数据和相对信息
        leader_pose = self.node_data[self.leader_nodes[0]]['pose'] if self.leader_nodes else {'x': 0, 'y': 0}
        
        for follower in self.follower_nodes:
            pose = self.node_data[follower]['pose']
            vel = self.node_data[follower]['velocity']
            
            distance, azimuth = self.calculate_relative_info(pose, leader_pose)
            
            row.extend([
                pose['x'], pose['y'], pose['heading'],
                vel['vx'], vel['vy'], vel['wz'],
                distance, azimuth
            ])
        
        # 集群统计信息
        centroid_x, centroid_y, variance, avg_nn_dist, avg_lf_dist = self.calculate_swarm_statistics()
        row.extend([centroid_x, centroid_y, variance, avg_nn_dist, avg_lf_dist])
        
        # 写入CSV
        self.csv_writer.writerow(row)
        self.csv_file.flush()
    
    def __del__(self):
        """析构函数"""
        if hasattr(self, 'csv_file'):
            self.csv_file.close()
            rospy.loginfo(f"📁 CSV文件已保存: {self.csv_filename}")

if __name__ == '__main__':
    try:
        node = CSVLoggerNode()
        rospy.spin()
    except rospy.ROSInterruptException:
        rospy.loginfo("👋 CSV日志记录器退出")
    except Exception as e:
        rospy.logerr(f"❌ CSV日志记录器错误: {e}")
