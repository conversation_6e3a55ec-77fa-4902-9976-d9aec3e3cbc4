#!/usr/bin/env python3
"""
简化2D空间感知灯语集群实验 - 修复版本
专注于地面车辆的实用场景，去掉俯仰角，专注于距离和方位角

核心特点：
1. 2D空间感知（距离 + 方位角）
2. 纯视觉灯语通信
3. 实用的集群行为
4. 简化的参数配置
5. 修复：使用全向移动而不是转向+前进
"""

import rospy
import threading
import time
import socket
import os
import shutil
import numpy as np
import math
from std_msgs.msg import Int32
from geometry_msgs.msg import Twist
from sensor_msgs.msg import Image
from cv_bridge import CvBridge
from ultralytics import YOLO
from newan import analyze_flash_patterns_v5, map_patterns_to_numbers, analyze_flash_patterns_v5_multi_target, map_patterns_to_numbers_multi_target
# from cal_vec import calculate_position_vector, create_camera_matrix
from cal_vec import calculate_position_vector_planar, create_camera_matrix, find_multi_target_distances_planar
from gazebo_msgs.srv import GetModelState
import tf.transformations as tf_trans

class Simple2DSwarmExperiment:
    """简化2D空间感知集群实验控制器"""
    
    def __init__(self):
        rospy.init_node('simple_2d_swarm_experiment', anonymous=True)
        self.node_name = socket.gethostname()
        
        # 简化的2D集群指令
        self.SWARM_COMMANDS = {
            1: "approach",        # 靠近
            2: "follow",          # 跟随
            3: "avoid",           # 避让
            4: "circle",          # 环绕
            5: "stop",            # 停止
            6: "align",           # 对齐
            7: "retreat",         # 后退
            8: "parallel"         # 平行
        }
        
        # 2D空间参数
        self.spatial_params = {
            'target_distance': 2.0,    # 目标距离(米) - approach的目标
            'safe_distance': 1.5,      # 最小安全距离(米) - 增加到1.5米防止重叠
            'follow_distance': 2.5,    # 跟随距离(米)
            'max_distance': 10.0,      # 最大检测距离(米)
            'angle_tolerance': 30.0,   # 角度容忍度(度)
            'approach_speed': 0.5,     # 靠近速度
            'retreat_speed': 0.4,      # 后退速度 - 稍微增加避障速度
            'turn_speed': 0.4          # 转向速度
        }


        # 摄像头配置 - 相对于世界坐标系的朝向角度
        # 根据实际测试验证的摄像头朝向：
        # 坐标系：0°=+Y轴，90°=+X轴，180°=-Y轴，270°=-X轴
        self.camera_orientations = {
            'cam0': 270,    # -X轴方向 (linear.x < 0)
            'cam1': 180,    # -Y轴方向 (linear.y < 0)
            'cam2': 90,     # +X轴方向 (linear.x > 0)
            'cam3': 0       # +Y轴方向 (linear.y > 0)
        }
        
        # 实验配置
        self.config = {
            'detection_duration': 5,
            'cameras': ['/cam0/image_raw', '/cam1/image_raw', '/cam2/image_raw', '/cam3/image_raw'],
            # 'cameras': [ '/cam2/image_raw', '/cam3/image_raw'],
            'base_path': '/home/<USER>/nnDataset/0920/labels',
            'real_width': 0.31,
            # 智能早停配置
            'early_stop_enabled': True,          # 是否启用智能早停
            'early_stop_check_interval': 1.0,    # 检查间隔(秒)
            'early_stop_no_target_duration': 2.0, # 无目标持续时间阈值(秒)
            'min_detection_duration': 1.0,       # 最小检测时间(秒)
            # 灯语分析配置（理论最优参数）
            'flash_num_frames': 36,              # 分析窗口大小(帧)
            'flash_start_frame': 7,              # 起始帧
            'flash_threshold': 0.2               # 比例容忍度
        }
        
        # 初始化组件
        self.setup_publishers()
        self.setup_vision_system()
        self.setup_gazebo_services()

        # 2D空间状态 - 支持多目标
        self.detected_targets_2d = []
        self.track_targets_2d = {}  # 按TrackID存储多目标信息
        self.current_behavior = "idle"

        rospy.loginfo(f"🚗 Simple2DSwarmExperiment initialized on {self.node_name}")
    
    def setup_publishers(self):
        """设置发布者"""
        led_topic = f'/{self.node_name}/led_mode'
        vel_topic = f'/{self.node_name}/vel_cmd'

        self.led_pub = rospy.Publisher(led_topic, Int32, queue_size=10)
        self.vel_pub = rospy.Publisher(vel_topic, Twist, queue_size=10)

        rospy.loginfo(f"📡 设置发布者:")
        rospy.loginfo(f"   LED话题: {led_topic}")
        rospy.loginfo(f"   速度话题: {vel_topic}")

        time.sleep(1)
    
    def setup_vision_system(self):
        """设置视觉系统"""
        self.bridge = CvBridge()
        self.camera_matrix = create_camera_matrix()
        self.camera_matrix_inv = np.linalg.inv(self.camera_matrix)

    def setup_gazebo_services(self):
        """设置Gazebo服务"""
        rospy.loginfo("🔧 等待Gazebo服务...")
        rospy.wait_for_service('/gazebo/get_model_state')
        self.get_model_state = rospy.ServiceProxy('/gazebo/get_model_state', GetModelState)
        rospy.loginfo("✅ Gazebo服务连接成功")

    def get_vehicle_heading(self):
        """获取车辆当前朝向（偏航角）"""
        try:
            model_state = self.get_model_state(self.node_name, '')
            orientation = model_state.pose.orientation
            euler = tf_trans.euler_from_quaternion([
                orientation.x, orientation.y, orientation.z, orientation.w
            ])
            yaw = euler[2]  # 偏航角（弧度）
            yaw_degrees = math.degrees(yaw)  # 转换为度

            # 标准化到[0, 360)范围
            if yaw_degrees < 0:
                yaw_degrees += 360

            return yaw_degrees
        except Exception as e:
            rospy.logwarn(f"⚠️ 获取车辆朝向失败: {e}")
            return 0.0  # 默认朝向北方

    def get_direction_description(self, angle):
        """根据角度返回方向描述"""
        angle = angle % 360
        if angle < 22.5 or angle >= 337.5:
            return "北方"
        elif angle < 67.5:
            return "东北方"
        elif angle < 112.5:
            return "东方"
        elif angle < 157.5:
            return "东南方"
        elif angle < 202.5:
            return "南方"
        elif angle < 247.5:
            return "西南方"
        elif angle < 292.5:
            return "西方"
        else:
            return "西北方"
    
    def send_led_command(self, mode):
        """发送LED指令"""
        msg = Int32()
        msg.data = mode
        self.led_pub.publish(msg)
        action = self.SWARM_COMMANDS.get(mode, "未知")
        rospy.loginfo(f"🔆 发送LED指令: 模式{mode} → {action}")
    
    def start_2d_detection(self):
        """启动2D空间检测 - 支持多目标"""
        rospy.loginfo("🎥 启动2D空间检测（多目标模式）...")

        # 清空之前的检测结果
        self.detected_targets_2d = []
        self.track_targets_2d = {}
        self.all_camera_detections = {}  # 清空检测结果缓存
        
        # 启动多摄像头检测
        detection_threads = []
        for camera in self.config['cameras']:
            thread = threading.Thread(target=self.detect_camera_2d, args=(camera,))
            thread.start()
            detection_threads.append(thread)
        
        for thread in detection_threads:
            thread.join()
            
        # 分析2D空间信息
        self.analyze_2d_spatial_info_multi_target()
    
    def detect_camera_2d(self, camera_topic):
        """2D空间检测（检测期间只缓存，检测结束后统一空间分析）- 支持智能早停"""
        cam_name = camera_topic.split('/')[-2]
        label_path = f"{self.config['base_path']}/{cam_name}"

        if os.path.exists(label_path):
            shutil.rmtree(label_path)
        os.makedirs(label_path, exist_ok=True)

        bridge = CvBridge()
        model = YOLO('/home/<USER>/1206Cars-11/weights/best.engine')
        frame_count = 0
        start_time = time.time()

        # 用于批量缓存label内容和YOLO检测结果
        all_labels = []  # [(frame_count, [line1, line2, ...]), ...]
        all_detections = []  # [(frame_count, [tuple(track_id, class_id, x_center, y_center, width, height)])]

        # 智能早停相关变量
        last_target_time = start_time  # 最后一次检测到目标的时间
        detection_stopped = False      # 检测是否已停止

        def image_callback(msg):
            nonlocal frame_count, start_time, last_target_time, detection_stopped

            current_time = time.time()

            # 检查是否超过最大检测时间
            if current_time - start_time > self.config['detection_duration']:
                return

            # 检查智能早停条件
            if self.config.get('early_stop_enabled', False) and not detection_stopped:
                # 确保至少检测了最小时间
                if current_time - start_time >= self.config.get('min_detection_duration', 1.0):
                    # 检查是否长时间无目标
                    no_target_duration = current_time - last_target_time
                    if no_target_duration >= self.config.get('early_stop_no_target_duration', 2.0):
                        detection_stopped = True
                        rospy.loginfo(f"⏹️ {cam_name}: 智能早停 - {no_target_duration:.1f}s无目标检测")
                        return

            cv_image = bridge.imgmsg_to_cv2(msg, desired_encoding='bgr8')
            results = model.track(cv_image, imgsz=(640, 384), stream=True)

            # 每帧只处理一次，合并所有结果
            label_lines = []
            detections = []
            has_valid_target = False  # 标记是否有有效目标

            for result in results:
                if result.boxes is not None and len(result.boxes) > 0:
                    # 批量处理所有boxes，减少GPU-CPU转换次数
                    boxes_data = result.boxes.xywh.cpu().numpy()  # 一次性转换所有boxes
                    cls_data = result.boxes.cls.cpu().numpy() if result.boxes.cls is not None else None
                    id_data = result.boxes.id.cpu().numpy() if result.boxes.id is not None else None

                    for i, box_xywh in enumerate(boxes_data):
                        cls = int(cls_data[i]) if cls_data is not None else -1
                        track_id = int(id_data[i]) if id_data is not None else -1
                        x_center, y_center, width, height = box_xywh

                        # 写入label行
                        label_lines.append(f"{track_id} {cls} {x_center:.2f} {y_center:.2f} {width:.2f} {height:.2f}\n")

                        # 简化检测结果存储
                        detections.append((track_id, cls, x_center, y_center, width, height))

                        # 检查是否为有效目标（非110类别）
                        if cls != 110:
                            has_valid_target = True

            # 更新最后检测到目标的时间
            if has_valid_target:
                last_target_time = current_time

            # 每帧只添加一次结果
            all_labels.append((frame_count, label_lines))
            all_detections.append((frame_count, detections))
            frame_count += 1

        sub = rospy.Subscriber(camera_topic, Image, image_callback)

        # 动态等待：要么达到最大时间，要么智能早停
        while not rospy.is_shutdown():
            current_time = time.time()
            if current_time - start_time > self.config['detection_duration']:
                break
            if detection_stopped:
                break
            time.sleep(0.1)  # 短暂休眠避免CPU占用过高

        sub.unregister()
        
        # 检测结束后批量写入label
        try:
            for frame_count, label_lines in all_labels:
                with open(f"{label_path}/{frame_count}.txt", 'w') as f:
                    f.writelines(label_lines)

            # 计算实际检测时间和统计信息
            actual_duration = time.time() - start_time

            # 统计信息
            total_detections = sum(len(detections) for _, detections in all_detections)
            valid_detections = sum(1 for _, detections in all_detections
                                 for det in detections if det[1] != 110)  # det[1]是cls
            frames_with_targets = sum(1 for _, detections in all_detections
                                    if any(det[1] != 110 for det in detections))

            # 注释掉详细的检测完成日志
            # status_msg = f"✅ {cam_name}: 2D检测完成，{frame_count}帧"
            # if detection_stopped:
            #     status_msg += f"（智能早停 {actual_duration:.1f}s）"
            # else:
            #     status_msg += f"（完整检测 {actual_duration:.1f}s）"
            # status_msg += f"，有效检测{valid_detections}个（{frames_with_targets}帧有目标），已批量写入label"
            #
            # rospy.loginfo(status_msg)
        except Exception as e:
            rospy.logwarn(f"⚠️ 批量写入label失败: {e}")
        
        # 检测结束后只缓存检测结果，不进行空间计算（优化FPS）
        # 将检测结果存储到实例变量中，供后续分析使用
        if not hasattr(self, 'all_camera_detections'):
            self.all_camera_detections = {}
        self.all_camera_detections[cam_name] = all_detections

        rospy.loginfo(f"📦 {cam_name}: 检测结果已缓存，等待统一分析")

    def analyze_2d_spatial_info_multi_target(self):
        """分析2D空间信息 - 批量优化版本"""
        rospy.loginfo("🧠 分析2D空间信息（批量优化版本）...")

        # 检查是否有缓存的检测结果
        if not hasattr(self, 'all_camera_detections') or not self.all_camera_detections:
            rospy.logwarn("❌ 未找到检测结果缓存")
            return

        # 指令优先级定义（数值越小优先级越高）
        COMMAND_PRIORITY = {
            "avoid": 1,
            "retreat": 2,
            "approach": 3,
            "follow": 4,
            "circle": 5,
            "align": 6,
            "parallel": 7,
            "stop": 8
        }

        # 1. 先对所有检测到的目标进行空间计算并输出
        rospy.loginfo("📐 第一步：空间计算（所有检测目标）...")

        for cam_name, cam_detections in self.all_camera_detections.items():
            # 按TrackID分组检测结果
            track_detections = {}
            for frame_count, detections in cam_detections:
                for det in detections:
                    # det是元组格式: (track_id, cls, x_center, y_center, width, height)
                    track_id, cls, x_center, y_center, width, height = det
                    if cls != 110:  # 排除特定类别
                        if track_id not in track_detections:
                            track_detections[track_id] = []
                        track_detections[track_id].append((x_center, y_center, width, height))

            # 计算并输出每个TrackID的空间信息
            if track_detections:
                rospy.loginfo(f"📊 {cam_name} 检测目标空间信息:")
                for track_id, detections in track_detections.items():
                    distances = []
                    azimuths = []
                    positions_2d = []

                    for det in detections:
                        try:
                            x_center, y_center, width, height = det
                            position_vector, distance, theta, azimuth, elevation = calculate_position_vector_planar(
                                width, x_center, y_center, self.camera_matrix_inv, self.config['real_width']
                            )
                            distances.append(distance)
                            azimuths.append(azimuth)
                            positions_2d.append([position_vector[0], position_vector[1]])
                        except Exception as e:
                            continue

                    if distances:
                        avg_distance = np.mean(distances)
                        avg_azimuth = np.mean(azimuths)
                        avg_position_2d = np.mean(positions_2d, axis=0)
                        rospy.loginfo(f"  TrackID{track_id}: 距离={avg_distance:.2f}m, 方位={avg_azimuth:.1f}°, 位置=[{avg_position_2d[0]:.2f}, {avg_position_2d[1]:.2f}], 检测次数={len(detections)}")

        # 2. 进行灯语分析，找出有效指令的目标
        rospy.loginfo("� 第一步：灯语分析...")
        valid_targets = []  # [(cam_name, track_id, pattern, number, behavior, priority)]

        for cam_name in self.all_camera_detections.keys():
            label_path = f"{self.config['base_path']}/{cam_name}"
            try:
                all_track_patterns = analyze_flash_patterns_v5_multi_target(
                    label_path,
                    self.config['flash_num_frames'],
                    start_from_frame=self.config['flash_start_frame'],
                    threshold=self.config['flash_threshold']
                )
                track_numbers = map_patterns_to_numbers_multi_target(all_track_patterns)

                for track_id, patterns in all_track_patterns.items():
                    if track_id in track_numbers:
                        numbers = track_numbers[track_id]
                        for pattern, number in zip(patterns, numbers):
                            if pattern != "无闪烁" and number != -1 and number in self.SWARM_COMMANDS:
                                behavior = self.SWARM_COMMANDS[number]
                                priority = COMMAND_PRIORITY.get(behavior, 99)
                                valid_targets.append((cam_name, track_id, pattern, number, behavior, priority))
                                rospy.loginfo(f"   ✅ 发现有效指令: {cam_name} TrackID{track_id} → {behavior}")
            except Exception as e:
                rospy.logwarn(f"   ❌ {cam_name} 灯语分析失败: {e}")

        if not valid_targets:
            rospy.logwarn("❌ 没有检测到有效的集群指令")
            return

        # 3. 只对有效指令的目标进行空间计算
        rospy.loginfo("📐 第三步：空间计算（仅针对有效指令目标）...")
        candidates = []

        for cam_name, track_id, pattern, number, behavior, priority in valid_targets:
            # 找到该摄像头该TrackID的所有检测结果
            cam_detections = self.all_camera_detections[cam_name]
            target_detections = []

            for frame_count, detections in cam_detections:
                for det in detections:
                    # det是元组格式: (track_id, cls, x_center, y_center, width, height)
                    det_track_id, cls, x_center, y_center, width, height = det
                    if det_track_id == track_id and cls != 110:
                        target_detections.append((x_center, y_center, width, height))

            if not target_detections:
                rospy.logwarn(f"   ⚠️ {cam_name} TrackID{track_id} 没有有效检测结果")
                continue

            # 计算平均空间信息
            distances = []
            azimuths = []
            positions_2d = []

            for det in target_detections:
                try:
                    # det是元组格式: (x_center, y_center, width, height)
                    x_center, y_center, width, height = det
                    position_vector, distance, theta, azimuth, elevation = calculate_position_vector_planar(
                        width, x_center, y_center, self.camera_matrix_inv, self.config['real_width']
                    )
                    distances.append(distance)
                    azimuths.append(azimuth)
                    positions_2d.append([position_vector[0], position_vector[1]])
                except Exception as e:
                    rospy.logwarn(f"   ⚠️ 空间计算失败: {e}")
                    continue

            if distances:
                avg_distance = np.mean(distances)
                avg_azimuth = np.mean(azimuths)
                avg_position_2d = np.mean(positions_2d, axis=0)

                candidates.append({
                    'track_id': track_id,
                    'number': number,
                    'pattern': pattern,
                    'behavior': behavior,
                    'priority': priority,
                    'distance': avg_distance,
                    'azimuth': avg_azimuth,
                    'position_2d': avg_position_2d,
                    'cam_name': cam_name
                })
                rospy.loginfo(f"   📊 {cam_name} TrackID{track_id}: 距离={avg_distance:.2f}m, 方位={avg_azimuth:.1f}°")

        if not candidates:
            rospy.logwarn("❌ 空间计算后没有有效候选目标")
            return

        # 4. 选择最优目标并执行
        candidates.sort(key=lambda x: (x['priority'], x['distance']))
        best = candidates[0]

        rospy.loginfo(f"⭐ 选择执行目标: TrackID {best['track_id']} | 指令: {best['behavior']} | 距离: {best['distance']:.2f}m | 摄像头: {best['cam_name']}")
        rospy.loginfo(f"   所有候选目标:")
        for c in candidates:
            rospy.loginfo(f"     TrackID {c['track_id']} | 指令: {c['behavior']} | 优先级: {c['priority']} | 距离: {c['distance']:.2f}m | 摄像头: {c['cam_name']}")

        # 执行最优目标的行为
        self.execute_2d_behavior_multi_target(
            best['number'], best['distance'], best['azimuth'], best['cam_name'], best['track_id']
        )

    def analyze_2d_spatial_info(self):
        """分析2D空间信息 - 向后兼容版本"""
        rospy.loginfo("🧠 分析2D空间信息...")

        if not self.detected_targets_2d:
            rospy.logwarn("❌ 未检测到任何目标")
            return

        # 按摄像头分组
        camera_analysis = {}
        for target in self.detected_targets_2d:
            cam = target['camera']
            if cam not in camera_analysis:
                camera_analysis[cam] = []
            camera_analysis[cam].append(target)

        # 分析每个摄像头
        for cam_name, targets in camera_analysis.items():
            rospy.loginfo(f"\n📹 {cam_name.upper()} 2D空间分析:")

            if not targets:
                continue

            # 计算平均2D信息
            avg_distance = np.mean([t['distance'] for t in targets])
            avg_azimuth = np.mean([t['azimuth'] for t in targets])
            avg_position_2d = np.mean([t['position_2d'] for t in targets], axis=0)

            rospy.loginfo(f"   📏 平均距离: {avg_distance:.2f}米")
            rospy.loginfo(f"   🧭 平均方位角: {avg_azimuth:.1f}°")
            rospy.loginfo(f"   📍 平均2D位置: [{avg_position_2d[0]:.2f}, {avg_position_2d[1]:.2f}]")

            # 识别灯语指令
            label_path = f"{self.config['base_path']}/{cam_name}"
            try:
                patterns = analyze_flash_patterns_v5(
                    label_path,
                    self.config['flash_num_frames'],
                    start_from_frame=self.config['flash_start_frame'],
                    threshold=self.config['flash_threshold']
                )
                numbers = map_patterns_to_numbers(patterns)

                for pattern, number in zip(patterns, numbers):
                    if pattern != "无闪烁" and number != -1 and number in self.SWARM_COMMANDS:
                        rospy.loginfo(f"   🔆 检测到灯语: {pattern} → 指令{number} ({self.SWARM_COMMANDS[number]})")

                        # 执行2D集群行为 - 传入摄像头信息
                        self.execute_2d_behavior(number, avg_distance, avg_azimuth, cam_name)

            except Exception as e:
                rospy.logwarn(f"   ❌ 灯语分析失败: {e}")

    def execute_2d_behavior_multi_target(self, command_id, distance, azimuth, camera_name, track_id):
        """执行2D集群行为 - 多目标版本"""
        if command_id not in self.SWARM_COMMANDS:
            return

        behavior = self.SWARM_COMMANDS[command_id]
        self.current_behavior = behavior

        rospy.loginfo(f"🚀 执行2D行为 (TrackID {track_id}): {behavior}")
        rospy.loginfo(f"   📊 2D参数: 距离{distance:.2f}m, 方位{azimuth:.1f}°")
        rospy.loginfo(f"   📹 检测摄像头: {camera_name} (朝向{self.camera_orientations.get(camera_name, 'unknown')}°)")

        twist = Twist()

        # 简化的2D行为逻辑 - 使用全向移动
        if behavior == "approach":
            self.approach_2d(twist, distance, azimuth, camera_name)
        elif behavior == "follow":
            self.follow_2d(twist, distance, azimuth, camera_name)
        elif behavior == "avoid":
            self.avoid_2d(twist, distance, azimuth, camera_name)
        elif behavior == "circle":
            self.circle_2d(twist, distance, azimuth, camera_name)
        elif behavior == "stop":
            twist.linear.x = 0.0
            twist.linear.y = 0.0
            twist.angular.z = 0.0
        elif behavior == "align":
            self.align_2d(twist, azimuth, camera_name)
        elif behavior == "retreat":
            self.retreat_2d(twist, azimuth, camera_name)
        elif behavior == "parallel":
            self.parallel_2d(twist, azimuth, camera_name)

        # 检查是否有实际的运动指令
        if abs(twist.linear.x) < 0.01 and abs(twist.linear.y) < 0.01 and abs(twist.angular.z) < 0.01:
            rospy.logwarn("⚠️ 计算出的运动指令接近零，可能参数设置有问题")
            rospy.logwarn(f"   距离: {distance:.2f}m, 安全距离: {self.spatial_params['safe_distance']:.2f}m")
            rospy.logwarn(f"   方位角: {azimuth:.1f}°, 容忍度: {self.spatial_params['angle_tolerance']:.1f}°")

        # 发布并执行
        rospy.loginfo(f"📤 发布运动指令 (TrackID {track_id}): linear.x={twist.linear.x:.3f}, linear.y={twist.linear.y:.3f}, angular.z={twist.angular.z:.3f}")
        rospy.loginfo(f"📡 发布到话题: /{self.node_name}/vel_cmd")

        # 检查话题连接状态
        if self.vel_pub.get_num_connections() == 0:
            rospy.logwarn("⚠️ 警告: 速度话题没有订阅者！")
            rospy.logwarn("   请检查VelControl节点是否正在运行")
        else:
            rospy.loginfo(f"✅ 话题连接正常，有 {self.vel_pub.get_num_connections()} 个订阅者")

        # 调试：打印完整的Twist消息内容
        rospy.loginfo(f"🔍 完整Twist消息 (TrackID {track_id}):")
        rospy.loginfo(f"   linear: x={twist.linear.x}, y={twist.linear.y}, z={twist.linear.z}")
        rospy.loginfo(f"   angular: x={twist.angular.x}, y={twist.angular.y}, z={twist.angular.z}")

        # 发布消息
        self.vel_pub.publish(twist)
        rospy.loginfo("✅ 消息已发布")

        # 根据行为类型调整执行时间
        if behavior == "stop":
            execution_time = 1.0
        elif behavior in ["approach", "retreat"]:
            # 根据距离计算执行时间，但限制在合理范围内
            max_speed = max(abs(twist.linear.x), abs(twist.linear.y), 0.1)
            execution_time = min(max(distance / max_speed, 2.0), 10.0)-0.5
        else:
            execution_time = 4.0

        rospy.loginfo(f"⏱️ 执行运动指令 {execution_time:.1f} 秒...")

        # 分段执行，便于观察
        steps = int(execution_time * 2)  # 每0.5秒发布一次
        for i in range(steps):
            if rospy.is_shutdown():
                break
            self.vel_pub.publish(twist)
            time.sleep(0.5)
            if i % 4 == 0:  # 每2秒打印一次状态
                rospy.loginfo(f"   执行中... {i*0.5:.1f}/{execution_time:.1f}秒")

        # 停止
        stop_twist = Twist()
        rospy.loginfo("🛑 发布停止指令")
        self.vel_pub.publish(stop_twist)
        rospy.loginfo(f"✅ 2D行为 {behavior} (TrackID {track_id}) 完成")

    def execute_2d_behavior(self, command_id, distance, azimuth, camera_name):
        """执行2D集群行为 - 向后兼容版本"""
        if command_id not in self.SWARM_COMMANDS:
            return

        behavior = self.SWARM_COMMANDS[command_id]
        self.current_behavior = behavior

        rospy.loginfo(f"🚀 执行2D行为: {behavior}")
        rospy.loginfo(f"   📊 2D参数: 距离{distance:.2f}m, 方位{azimuth:.1f}°")
        rospy.loginfo(f"   📹 检测摄像头: {camera_name} (朝向{self.camera_orientations.get(camera_name, 'unknown')}°)")

        twist = Twist()

        # 简化的2D行为逻辑 - 使用全向移动
        if behavior == "approach":
            self.approach_2d(twist, distance, azimuth, camera_name)
        elif behavior == "follow":
            self.follow_2d(twist, distance, azimuth, camera_name)
        elif behavior == "avoid":
            self.avoid_2d(twist, distance, azimuth, camera_name)
        elif behavior == "circle":
            self.circle_2d(twist, distance, azimuth, camera_name)
        elif behavior == "stop":
            twist.linear.x = 0.0
            twist.linear.y = 0.0
            twist.angular.z = 0.0
        elif behavior == "align":
            self.align_2d(twist, azimuth, camera_name)
        elif behavior == "retreat":
            self.retreat_2d(twist, azimuth, camera_name)
        elif behavior == "parallel":
            self.parallel_2d(twist, azimuth, camera_name)

        # 检查是否有实际的运动指令
        if abs(twist.linear.x) < 0.01 and abs(twist.linear.y) < 0.01 and abs(twist.angular.z) < 0.01:
            rospy.logwarn("⚠️ 计算出的运动指令接近零，可能参数设置有问题")
            rospy.logwarn(f"   距离: {distance:.2f}m, 安全距离: {self.spatial_params['safe_distance']:.2f}m")
            rospy.logwarn(f"   方位角: {azimuth:.1f}°, 容忍度: {self.spatial_params['angle_tolerance']:.1f}°")

        # 发布并执行
        rospy.loginfo(f"📤 发布运动指令: linear.x={twist.linear.x:.3f}, linear.y={twist.linear.y:.3f}, angular.z={twist.angular.z:.3f}")
        rospy.loginfo(f"📡 发布到话题: /{self.node_name}/vel_cmd")

        # 检查话题连接状态
        if self.vel_pub.get_num_connections() == 0:
            rospy.logwarn("⚠️ 警告: 速度话题没有订阅者！")
            rospy.logwarn("   请检查VelControl节点是否正在运行")
        else:
            rospy.loginfo(f"✅ 话题连接正常，有 {self.vel_pub.get_num_connections()} 个订阅者")

        # 调试：打印完整的Twist消息内容
        rospy.loginfo(f"🔍 完整Twist消息:")
        rospy.loginfo(f"   linear: x={twist.linear.x}, y={twist.linear.y}, z={twist.linear.z}")
        rospy.loginfo(f"   angular: x={twist.angular.x}, y={twist.angular.y}, z={twist.angular.z}")

        # 发布消息
        self.vel_pub.publish(twist)
        rospy.loginfo("✅ 消息已发布")

        # 根据行为类型调整执行时间
        if behavior == "stop":
            execution_time = 1.0
        elif behavior in ["approach", "retreat"]:
            # 根据距离计算执行时间，但限制在合理范围内
            max_speed = max(abs(twist.linear.x), abs(twist.linear.y), 0.1)
            execution_time = min(max(distance / max_speed, 2.0), 10.0)-0.5
        else:
            execution_time = 4.0

        rospy.loginfo(f"⏱️ 执行运动指令 {execution_time:.1f} 秒...")

        # 分段执行，便于观察
        steps = int(execution_time * 2)  # 每0.5秒发布一次
        for i in range(steps):
            if rospy.is_shutdown():
                break
            self.vel_pub.publish(twist)
            time.sleep(0.5)
            if i % 4 == 0:  # 每2秒打印一次状态
                rospy.loginfo(f"   执行中... {i*0.5:.1f}/{execution_time:.1f}秒")

        # 停止
        stop_twist = Twist()
        rospy.loginfo("🛑 发布停止指令")
        self.vel_pub.publish(stop_twist)
        rospy.loginfo(f"✅ 2D行为 {behavior} 完成")

    def approach_2d(self, twist, distance, azimuth, camera_name):
        """
        2D靠近行为 - 修复版本，使用全向移动
        核心思想：直接计算目标方向，使用linear.x和linear.y进行全向移动
        """
        target_distance = self.spatial_params['target_distance']
        safe_distance = self.spatial_params['safe_distance']

        rospy.loginfo(f"🎯 approach_2d: 当前距离{distance:.2f}m")
        rospy.loginfo(f"🎯 目标距离{target_distance:.2f}m, 最小安全距离{safe_distance:.2f}m")
        rospy.loginfo(f"🎯 方位角{azimuth:.1f}°, 检测摄像头{camera_name}")

        # 距离检查
        if distance <= target_distance:
            rospy.loginfo(f"🎯 ✅ 已到达目标距离，停止靠近")
            return

        # 🔧 新逻辑：直接根据摄像头朝向和检测方向计算目标世界坐标方向
        camera_orientation = self.camera_orientations.get(camera_name, 0)

        # 计算目标在世界坐标系中的方向角度
        target_world_angle = (camera_orientation + azimuth) % 360

        rospy.loginfo(f"🎯 全向移动分析:")
        rospy.loginfo(f"   📹 检测摄像头{camera_name}朝向: {camera_orientation}°")
        rospy.loginfo(f"   📐 相对摄像头偏差: {azimuth:.1f}°")
        rospy.loginfo(f"   🌍 目标世界方向: {target_world_angle:.1f}°")

        # 🔧 核心逻辑：将世界方向角度转换为linear.x和linear.y
        # 世界坐标系：0°=北(+Y), 90°=东(+X), 180°=南(-Y), 270°=西(-X)
        target_angle_rad = math.radians(target_world_angle)

        # 计算移动速度的X和Y分量
        speed = self.spatial_params['approach_speed']

        # 注意：世界坐标系中0°是北方(+Y)，90°是东方(+X)
        # 所以：X分量 = sin(angle), Y分量 = cos(angle)
        move_x = speed * math.sin(target_angle_rad)  # 东西方向
        move_y = speed * math.cos(target_angle_rad)  # 南北方向

        # 距离安全检查
        if distance < safe_distance:
            rospy.loginfo(f"🎯 ⚠️ 距离过近，减速")
            move_x *= 0.5
            move_y *= 0.5

        # 设置运动指令
        twist.linear.x = move_x
        twist.linear.y = move_y
        twist.angular.z = 0.0  # 不需要转向，直接全向移动

        # 确定移动方向描述
        direction_desc = self.get_direction_description(target_world_angle)

        rospy.loginfo(f"🎯 ✅ 全向移动策略: 朝向{direction_desc}")
        rospy.loginfo(f"🎯 控制指令: linear.x={twist.linear.x:.3f}, linear.y={twist.linear.y:.3f}")
        rospy.loginfo(f"🔍 approach_2d结束时: linear.x={twist.linear.x:.3f}, linear.y={twist.linear.y:.3f}")

    def follow_2d(self, twist, distance, azimuth, camera_name):
        """2D跟随行为 - 使用全向移动保持跟随距离"""
        target_distance = self.spatial_params['follow_distance']
        distance_error = distance - target_distance

        # 计算目标方向
        camera_orientation = self.camera_orientations.get(camera_name, 0)
        target_world_angle = (camera_orientation + azimuth) % 360
        target_angle_rad = math.radians(target_world_angle)

        rospy.loginfo(f"🔄 follow_2d: 目标距离{target_distance:.2f}m, 当前距离{distance:.2f}m, 误差{distance_error:.2f}m")
        rospy.loginfo(f"🔄 目标世界方向: {target_world_angle:.1f}°")

        # 距离控制
        if abs(distance_error) > 0.5:
            speed = self.spatial_params['approach_speed'] * 0.7
            if distance_error < 0:  # 距离太近，需要远离
                speed = -self.spatial_params['retreat_speed'] * 0.5

            # 计算移动方向
            move_x = speed * math.sin(target_angle_rad)
            move_y = speed * math.cos(target_angle_rad)

            twist.linear.x = move_x
            twist.linear.y = move_y

        twist.angular.z = 0.0
        rospy.loginfo(f"🔄 follow控制: linear.x={twist.linear.x:.3f}, linear.y={twist.linear.y:.3f}")

    def avoid_2d(self, twist, distance, azimuth, camera_name):
        """2D避让行为 - 使用全向移动远离目标"""
        # 计算目标方向
        camera_orientation = self.camera_orientations.get(camera_name, 0)
        target_world_angle = (camera_orientation + azimuth) % 360

        # 避让方向：与目标方向相反
        avoid_angle = (target_world_angle + 180) % 360
        avoid_angle_rad = math.radians(avoid_angle)

        rospy.loginfo(f"⚠️ avoid_2d: 距离{distance:.2f}m, 目标方向{target_world_angle:.1f}°")
        rospy.loginfo(f"⚠️ 避让方向: {avoid_angle:.1f}°")

        # 避让移动
        speed = self.spatial_params['retreat_speed']
        move_x = speed * math.sin(avoid_angle_rad)
        move_y = speed * math.cos(avoid_angle_rad)

        twist.linear.x = move_x
        twist.linear.y = move_y
        twist.angular.z = 0.0

        rospy.loginfo(f"⚠️ avoid控制: linear.x={twist.linear.x:.3f}, linear.y={twist.linear.y:.3f}")

    def circle_2d(self, twist, distance, azimuth, camera_name):
        """2D环绕行为 - 使用全向移动进行环绕"""
        # 计算目标方向
        camera_orientation = self.camera_orientations.get(camera_name, 0)
        target_world_angle = (camera_orientation + azimuth) % 360

        # 环绕方向：垂直于目标方向
        circle_angle = (target_world_angle + 90) % 360  # 逆时针环绕
        circle_angle_rad = math.radians(circle_angle)

        rospy.loginfo(f"🔄 circle_2d: 目标方向{target_world_angle:.1f}°, 环绕方向{circle_angle:.1f}°")

        # 根据距离调整速度
        target_distance = self.spatial_params['follow_distance']
        if distance > target_distance + 1.0:
            # 距离太远，向目标靠近一些
            target_angle_rad = math.radians(target_world_angle)
            approach_x = self.spatial_params['approach_speed'] * 0.3 * math.sin(target_angle_rad)
            approach_y = self.spatial_params['approach_speed'] * 0.3 * math.cos(target_angle_rad)
        else:
            approach_x = approach_y = 0

        # 环绕移动
        circle_speed = self.spatial_params['approach_speed'] * 0.7
        circle_x = circle_speed * math.sin(circle_angle_rad)
        circle_y = circle_speed * math.cos(circle_angle_rad)

        twist.linear.x = circle_x + approach_x
        twist.linear.y = circle_y + approach_y
        twist.angular.z = 0.0

        rospy.loginfo(f"🔄 circle控制: linear.x={twist.linear.x:.3f}, linear.y={twist.linear.y:.3f}")

    def align_2d(self, twist, azimuth, camera_name):
        """2D对齐行为 - 简化版本，只是停止"""
        rospy.loginfo(f"🎯 align_2d: 对齐行为，停止移动")
        twist.linear.x = 0.0
        twist.linear.y = 0.0
        twist.angular.z = 0.0

    def retreat_2d(self, twist, azimuth, camera_name):
        """2D后退行为 - 使用全向移动远离目标"""
        # 计算目标方向
        camera_orientation = self.camera_orientations.get(camera_name, 0)
        target_world_angle = (camera_orientation + azimuth) % 360

        # 后退方向：与目标方向相反
        retreat_angle = (target_world_angle + 180) % 360
        retreat_angle_rad = math.radians(retreat_angle)

        rospy.loginfo(f"🔙 retreat_2d: 目标方向{target_world_angle:.1f}°, 后退方向{retreat_angle:.1f}°")

        # 后退移动
        speed = self.spatial_params['retreat_speed']
        move_x = speed * math.sin(retreat_angle_rad)
        move_y = speed * math.cos(retreat_angle_rad)

        twist.linear.x = move_x
        twist.linear.y = move_y
        twist.angular.z = 0.0

        rospy.loginfo(f"🔙 retreat控制: linear.x={twist.linear.x:.3f}, linear.y={twist.linear.y:.3f}")

    def parallel_2d(self, twist, azimuth, camera_name):
        """2D平行行为 - 使用全向移动进行平行移动"""
        # 计算目标方向
        camera_orientation = self.camera_orientations.get(camera_name, 0)
        target_world_angle = (camera_orientation + azimuth) % 360

        # 平行方向：垂直于目标方向
        parallel_angle = (target_world_angle + 90) % 360
        parallel_angle_rad = math.radians(parallel_angle)

        rospy.loginfo(f"🔄 parallel_2d: 目标方向{target_world_angle:.1f}°, 平行方向{parallel_angle:.1f}°")

        # 平行移动
        speed = self.spatial_params['approach_speed']
        move_x = speed * math.sin(parallel_angle_rad)
        move_y = speed * math.cos(parallel_angle_rad)

        twist.linear.x = move_x
        twist.linear.y = move_y
        twist.angular.z = 0.0

        rospy.loginfo(f"🔄 parallel控制: linear.x={twist.linear.x:.3f}, linear.y={twist.linear.y:.3f}")

    def run_experiment(self):
        """运行实验（精简菜单）"""
        while not rospy.is_shutdown():
            print("\n" + "="*60)
            print("🚗 简化2D空间感知灯语集群实验 - 修复版本")
            print(f"节点: {self.node_name}")
            print("="*60)
            print("实验模式:")
            print("  1. 启动2D空间检测（多目标模式）")
            print("  2. 查看2D空间参数")
            print("  3. 调整2D参数")
            print("  4. 显示检测目标（多目标分析）")
            print("  q. 退出")
            print("="*60)

            try:
                choice = input("请选择: ").strip()

                if choice == '1':
                    self.start_2d_detection()
                elif choice == '2':
                    self.show_2d_params()
                elif choice == '3':
                    self.adjust_2d_params()
                elif choice == '4':
                    self.show_detected_targets_2d()
                elif choice.lower() == 'q':
                    break
                else:
                    print("无效选择")

            except KeyboardInterrupt:
                print("\n实验中断")
                break

    def show_2d_params(self):
        """显示2D参数"""
        print("\n⚙️ 2D空间参数:")
        for key, value in self.spatial_params.items():
            print(f"  {key}: {value}")

        print("\n📹 摄像头朝向配置:")
        for cam, angle in self.camera_orientations.items():
            direction = self.get_direction_description(angle)
            print(f"  {cam}: {angle}° ({direction})")

        print("\n🔧 检测配置:")
        print(f"  detection_duration: {self.config['detection_duration']}s")
        print(f"  real_width: {self.config['real_width']}m")

        print("\n⏹️ 智能早停配置:")
        print(f"  early_stop_enabled: {self.config.get('early_stop_enabled', False)}")
        if self.config.get('early_stop_enabled', False):
            print(f"  early_stop_no_target_duration: {self.config.get('early_stop_no_target_duration', 2.0)}s")
            print(f"  min_detection_duration: {self.config.get('min_detection_duration', 1.0)}s")
        else:
            print("  (智能早停已禁用)")

        print("\n🔆 灯语分析配置:")
        print(f"  flash_num_frames: {self.config.get('flash_num_frames', 36)}帧")
        print(f"  flash_start_frame: {self.config.get('flash_start_frame', 7)}")
        print(f"  flash_threshold: {self.config.get('flash_threshold', 0.2)}")

    def adjust_2d_params(self):
        """调整2D参数"""
        print("\n🔧 调整2D参数")
        print("="*50)
        print("1. 空间计算参数")
        print("2. 检测配置参数")
        print("3. 智能早停参数")
        print("="*50)

        choice = input("选择参数类型 (1-3): ").strip()

        if choice == '1':
            self.show_2d_params()
            param = input("输入要调整的参数名: ").strip()
            if param in self.spatial_params:
                try:
                    value = float(input(f"输入新值 (当前: {self.spatial_params[param]}): "))
                    self.spatial_params[param] = value
                    print(f"✅ {param} 已更新为 {value}")
                except ValueError:
                    print("❌ 无效数值")

        elif choice == '2':
            print(f"\n📹 检测配置参数:")
            print(f"  detection_duration: {self.config['detection_duration']}s")
            print(f"  real_width: {self.config['real_width']}m")

            param = input("输入要调整的参数名 (detection_duration/real_width): ").strip()
            if param in ['detection_duration', 'real_width']:
                try:
                    value = float(input(f"输入新值 (当前: {self.config[param]}): "))
                    self.config[param] = value
                    print(f"✅ {param} 已更新为 {value}")
                except ValueError:
                    print("❌ 无效数值")

        elif choice == '3':
            print(f"\n⏹️ 智能早停参数:")
            print(f"  early_stop_enabled: {self.config.get('early_stop_enabled', False)}")
            print(f"  early_stop_no_target_duration: {self.config.get('early_stop_no_target_duration', 2.0)}s")
            print(f"  min_detection_duration: {self.config.get('min_detection_duration', 1.0)}s")

            param = input("输入要调整的参数名: ").strip()
            if param == 'early_stop_enabled':
                value = input(f"启用智能早停? (y/n, 当前: {self.config.get(param, False)}): ").strip().lower()
                self.config[param] = value in ['y', 'yes', 'true', '1']
                print(f"✅ {param} 已更新为 {self.config[param]}")
            elif param in ['early_stop_no_target_duration', 'min_detection_duration']:
                try:
                    value = float(input(f"输入新值 (当前: {self.config.get(param, 0)}): "))
                    self.config[param] = value
                    print(f"✅ {param} 已更新为 {value}")
                except ValueError:
                    print("❌ 无效数值")
            else:
                print("❌ 无效参数名")
        else:
            print("❌ 无效选择")

    def show_detected_targets_2d(self):
        """显示2D检测目标 - 多目标版本"""
        print(f"\n🎯 2D检测目标 (共{len(self.detected_targets_2d)}个):")
        
        if self.track_targets_2d:
            print(f"📊 多目标分析 (按TrackID分组):")
            for track_id, targets in self.track_targets_2d.items():
                print(f"  TrackID {track_id}: {len(targets)}个检测点")
                if targets:
                    avg_distance = np.mean([t['distance'] for t in targets])
                    avg_azimuth = np.mean([t['azimuth'] for t in targets])
                    avg_position = np.mean([t['position_2d'] for t in targets], axis=0)
                    print(f"    平均距离: {avg_distance:.2f}m")
                    print(f"    平均方位角: {avg_azimuth:.1f}°")
                    print(f"    平均位置: [{avg_position[0]:.2f}, {avg_position[1]:.2f}]")
        
        # 显示最近的检测结果
        print(f"\n📋 最近检测结果:")
        for i, target in enumerate(self.detected_targets_2d[-10:]):
            track_id = target.get('track_id', 'N/A')
            confidence = target.get('confidence', 0.0)
            print(f"  {i+1}. TrackID:{track_id}, 摄像头:{target['camera']}, 置信度:{confidence:.3f}")
            print(f"     距离:{target['distance']:.2f}m, 方位:{target['azimuth']:.1f}°")
            print(f"     位置:[{target['position_2d'][0]:.2f}, {target['position_2d'][1]:.2f}]")

    def analyze_2d_spatial_info_multi_target_optimized(self):
        """分析2D空间信息 - 优化版本：先空间计算所有目标，再灯语分析"""
        rospy.loginfo("🧠 分析2D空间信息（优化版本：先空间后灯语）...")

        # 检查是否有缓存的检测结果
        if not hasattr(self, 'all_camera_detections') or not self.all_camera_detections:
            rospy.logwarn("❌ 未找到检测结果缓存")
            return

        # 指令优先级定义（数值越小优先级越高）
        COMMAND_PRIORITY = {
            "avoid": 1,
            "retreat": 2,
            "approach": 3,
            "follow": 4,
            "circle": 5,
            "align": 6,
            "parallel": 7,
            "stop": 8
        }

        # 1. 对所有检测到的目标进行空间计算
        rospy.loginfo("📐 第一步：空间计算（所有检测目标）...")
        all_spatial_targets = {}  # {(cam_name, track_id): spatial_info}

        for cam_name, cam_detections in self.all_camera_detections.items():
            # 按TrackID分组检测结果
            track_detections = {}
            for frame_count, detections in cam_detections:
                for det in detections:
                    # det现在是元组格式: (track_id, cls, x_center, y_center, width, height)
                    track_id, cls, x_center, y_center, width, height = det
                    if cls != 110:  # 排除特定类别
                        if track_id not in track_detections:
                            track_detections[track_id] = []
                        track_detections[track_id].append((x_center, y_center, width, height))

            # 计算每个TrackID的平均空间信息
            for track_id, detections in track_detections.items():
                distances = []
                azimuths = []
                positions_2d = []

                for det in detections:
                    try:
                        # det现在是元组格式: (x_center, y_center, width, height)
                        x_center, y_center, width, height = det
                        position_vector, distance, theta, azimuth, elevation = calculate_position_vector_planar(
                            width, x_center, y_center, self.camera_matrix_inv, self.config['real_width']
                        )
                        distances.append(distance)
                        azimuths.append(azimuth)
                        positions_2d.append([position_vector[0], position_vector[1]])
                    except Exception as e:
                        continue

                if distances:
                    avg_distance = np.mean(distances)
                    avg_azimuth = np.mean(azimuths)
                    avg_position_2d = np.mean(positions_2d, axis=0)

                    all_spatial_targets[(cam_name, track_id)] = {
                        'cam_name': cam_name,
                        'track_id': track_id,
                        'distance': avg_distance,
                        'azimuth': avg_azimuth,
                        'position_2d': avg_position_2d,
                        'detection_count': len(detections)
                    }

        # 输出所有目标的空间信息
        rospy.loginfo("📊 所有检测目标空间信息:")
        for (cam_name, track_id), spatial_info in all_spatial_targets.items():
            rospy.loginfo(f"  {cam_name} TrackID{track_id}: 距离={spatial_info['distance']:.2f}m, 方位={spatial_info['azimuth']:.1f}°, 位置=[{spatial_info['position_2d'][0]:.2f}, {spatial_info['position_2d'][1]:.2f}], 检测次数={spatial_info['detection_count']}")

        # 2. 生成label文件（仅在需要时）
        rospy.loginfo("📝 第二步：生成label文件...")
        for cam_name, cam_detections in self.all_camera_detections.items():
            label_path = f"{self.config['base_path']}/{cam_name}"
            for frame_count, detections in cam_detections:
                label_lines = []
                for det in detections:
                    track_id, cls, x_center, y_center, width, height = det
                    label_lines.append(f"{track_id} {cls} {x_center:.2f} {y_center:.2f} {width:.2f} {height:.2f}\n")

                # 写入label文件
                with open(f"{label_path}/{frame_count}.txt", 'w') as f:
                    f.writelines(label_lines)

        # 3. 进行灯语分析，找出有效指令的目标
        rospy.loginfo("🔍 第三步：灯语分析...")
        valid_targets = []  # [(cam_name, track_id, pattern, number, behavior, priority)]

        for cam_name in self.all_camera_detections.keys():
            label_path = f"{self.config['base_path']}/{cam_name}"
            try:
                all_track_patterns = analyze_flash_patterns_v5_multi_target(
                    label_path,
                    self.config['flash_num_frames'],
                    start_from_frame=self.config['flash_start_frame'],
                    threshold=self.config['flash_threshold']
                )
                track_numbers = map_patterns_to_numbers_multi_target(all_track_patterns)

                for track_id, patterns in all_track_patterns.items():
                    if track_id in track_numbers:
                        numbers = track_numbers[track_id]
                        for pattern, number in zip(patterns, numbers):
                            if pattern != "无闪烁" and number != -1 and number in self.SWARM_COMMANDS:
                                behavior = self.SWARM_COMMANDS[number]
                                priority = COMMAND_PRIORITY.get(behavior, 99)
                                valid_targets.append((cam_name, track_id, pattern, number, behavior, priority))
                                rospy.loginfo(f"   ✅ 发现有效指令: {cam_name} TrackID{track_id} → {behavior}")
            except Exception as e:
                rospy.logwarn(f"   ❌ {cam_name} 灯语分析失败: {e}")

        if not valid_targets:
            rospy.logwarn("❌ 没有检测到有效的集群指令")
            return

        # 4. 为有效指令目标准备候选列表（使用已计算的空间信息）
        rospy.loginfo("🎯 第四步：准备有效指令候选目标...")
        candidates = []

        for cam_name, track_id, pattern, number, behavior, priority in valid_targets:
            # 从已计算的空间信息中获取数据
            spatial_key = (cam_name, track_id)
            if spatial_key in all_spatial_targets:
                spatial_info = all_spatial_targets[spatial_key]
                candidates.append({
                    'track_id': track_id,
                    'number': number,
                    'pattern': pattern,
                    'behavior': behavior,
                    'priority': priority,
                    'distance': spatial_info['distance'],
                    'azimuth': spatial_info['azimuth'],
                    'position_2d': spatial_info['position_2d'],
                    'cam_name': cam_name
                })
            else:
                rospy.logwarn(f"   ⚠️ {cam_name} TrackID{track_id} 未找到空间信息")

        if not candidates:
            rospy.logwarn("❌ 没有有效候选目标")
            return

        # 5. 选择最优目标并执行
        candidates.sort(key=lambda x: (x['priority'], x['distance']))
        best = candidates[0]

        rospy.loginfo(f"⭐ 选择执行目标: TrackID {best['track_id']} | 指令: {best['behavior']} | 距离: {best['distance']:.2f}m | 摄像头: {best['cam_name']}")
        rospy.loginfo(f"   所有候选目标:")
        for c in candidates:
            rospy.loginfo(f"     TrackID {c['track_id']} | 指令: {c['behavior']} | 优先级: {c['priority']} | 距离: {c['distance']:.2f}m | 摄像头: {c['cam_name']}")

        # 执行最优目标的行为
        self.execute_2d_behavior_multi_target(
            best['number'], best['distance'], best['azimuth'], best['cam_name'], best['track_id']
        )

def main():
    try:
        experiment = Simple2DSwarmExperiment()
        experiment.run_experiment()
    except rospy.ROSInterruptException:
        print("ROS中断")
    except Exception as e:
        print(f"程序错误: {e}")

if __name__ == '__main__':
    main()
