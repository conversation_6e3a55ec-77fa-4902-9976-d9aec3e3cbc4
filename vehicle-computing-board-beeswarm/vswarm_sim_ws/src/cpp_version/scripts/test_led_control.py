#!/usr/bin/env python3
"""
测试LED控制功能的脚本
用于验证Xbox手柄控制器的LED功能是否正常工作
"""

import rospy
import socket
from std_msgs.msg import Int32

class LEDTestMonitor:
    def __init__(self):
        rospy.init_node('led_test_monitor', anonymous=True)
        
        # 获取当前节点名称
        self.node_name = socket.gethostname()
        
        # 订阅LED模式话题
        self.led_sub = rospy.Subscriber(f'/{self.node_name}/led_mode', Int32, self.led_callback)
        
        # LED模式描述
        self.mode_descriptions = {
            0: "常亮模式 - 淡蓝色 - 不闪烁",
            1: "慢闪烁 - 红色 - 5Hz",
            2: "慢闪烁 - 绿色 - 5Hz", 
            3: "慢闪烁 - 蓝色 - 5Hz",
            4: "慢闪烁 - 黄色 - 5Hz",
            5: "慢闪烁 - 紫色 - 5Hz",
            6: "快闪烁 - 红色 - 10Hz",
            7: "快闪烁 - 绿色 - 10Hz",
            8: "快闪烁 - 蓝色 - 10Hz",
            9: "快闪烁 - 黄色 - 10Hz",
            10: "快闪烁 - 紫色 - 10Hz"
        }
        
        print(f"🔍 LED测试监控器启动 - 监控节点: {self.node_name}")
        print(f"📡 订阅话题: /{self.node_name}/led_mode")
        print("等待LED模式变化...")
        print("=" * 60)
    
    def led_callback(self, msg):
        """LED模式回调函数"""
        mode = msg.data
        timestamp = rospy.Time.now()
        
        if mode in self.mode_descriptions:
            description = self.mode_descriptions[mode]
            print(f"[{timestamp.secs}.{timestamp.nsecs//1000000:03d}] 💡 LED模式变更: {mode} - {description}")
        else:
            print(f"[{timestamp.secs}.{timestamp.nsecs//1000000:03d}] ⚠️  未知LED模式: {mode}")
    
    def run(self):
        """运行监控器"""
        try:
            rospy.spin()
        except KeyboardInterrupt:
            print("\n👋 LED测试监控器退出")

def main():
    try:
        monitor = LEDTestMonitor()
        monitor.run()
    except rospy.ROSInterruptException:
        print("ROS中断")
    except Exception as e:
        print(f"程序错误: {e}")

if __name__ == '__main__':
    main()
