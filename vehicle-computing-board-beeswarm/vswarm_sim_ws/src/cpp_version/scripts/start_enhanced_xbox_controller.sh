#!/bin/bash
# 启动增强版Xbox手柄控制器的便捷脚本

echo "🎮 启动增强版Xbox手柄LED控制器"
echo "=================================="

# 检查ROS环境
if [ -z "$ROS_PACKAGE_PATH" ]; then
    echo "❌ ROS环境未设置，正在设置..."
    source /opt/ros/noetic/setup.bash
    source devel/setup.bash
fi

# 检查手柄设备
if [ ! -e "/dev/input/js0" ]; then
    echo "⚠️  警告: 未检测到Xbox手柄设备 (/dev/input/js0)"
    echo "请确保:"
    echo "  1. Xbox手柄已连接"
    echo "  2. 手柄驱动已安装"
    echo "  3. 设备权限正确"
    echo ""
    echo "继续启动控制器..."
fi

echo "🚀 启动增强版Xbox手柄控制器..."
echo "📋 功能说明:"
echo "  - 支持11种LED模式 (0-10)"
echo "  - A/X按钮: 切换LED模式"
echo "  - Y按钮: 重置到常亮模式"
echo "  - LB/RB: 快速切换红/绿色"
echo "  - Start: 运行演示模式"
echo "  - B按钮: 紧急停止"
echo "  - 左摇杆: 全向运动控制"
echo ""

# 启动控制器
roslaunch cpp_version xbox_controller_local.launch debug:=true
