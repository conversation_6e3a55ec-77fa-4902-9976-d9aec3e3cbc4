# Xbox手柄任意LED控制器使用说明

## 概述
任意LED控制版Xbox手柄控制器支持`interactive_led_control.py`中的所有LED功能，并实现了**任意LED模式直接选择**，无需顺序切换！

## 启动方法
```bash
# 启动增强版Xbox手柄控制器
roslaunch cpp_version xbox_controller_local.launch

# 或者带调试信息
roslaunch cpp_version xbox_controller_local.launch debug:=true
```

## 手柄按钮映射

### 🎮 运动控制
- **左摇杆**: 控制车辆全向移动
  - 上/下: 前进/后退 (linear.x)
  - 左/右: 左移/右移 (linear.y)
  - 注意: 纯全向运动，无需转向

### 💡 任意LED模式直接选择
**慢闪模式 (5Hz):**
- **A按钮**: 模式0 - 常亮 (淡蓝色)
- **X按钮**: 模式1 - 红色慢闪
- **Y按钮**: 模式2 - 绿色慢闪
- **LB按钮**: 模式3 - 蓝色慢闪
- **RB按钮**: 模式4 - 黄色慢闪
- **Back按钮**: 模式5 - 紫色慢闪

**快闪模式 (10Hz):**
- **十字键↑**: 模式6 - 红色快闪
- **十字键→**: 模式7 - 绿色快闪
- **十字键↓**: 模式8 - 蓝色快闪
- **十字键←**: 模式9 - 黄色快闪
- **左扳机(LT)**: 模式10 - 紫色快闪

**特殊功能:**
- **Start按钮**: 运行LED演示模式

### 🛑 安全控制
- **B按钮**: 紧急停止车辆运动

## LED模式说明
支持11种LED模式，与`interactive_led_control.py`完全一致：

| 模式 | 类型 | 颜色 | 频率 | 描述 |
|------|------|------|------|------|
| 0 | 常亮模式 | 淡蓝色 | 不闪烁 | 默认模式 |
| 1 | 慢闪烁 | 红色 | 5Hz | 警告状态 |
| 2 | 慢闪烁 | 绿色 | 5Hz | 正常状态 |
| 3 | 慢闪烁 | 蓝色 | 5Hz | 信息状态 |
| 4 | 慢闪烁 | 黄色 | 5Hz | 注意状态 |
| 5 | 慢闪烁 | 紫色 | 5Hz | 特殊状态 |
| 6 | 快闪烁 | 红色 | 10Hz | 紧急警告 |
| 7 | 快闪烁 | 绿色 | 10Hz | 快速确认 |
| 8 | 快闪烁 | 蓝色 | 10Hz | 快速信息 |
| 9 | 快闪烁 | 黄色 | 10Hz | 快速注意 |
| 10 | 快闪烁 | 紫色 | 10Hz | 快速特殊 |

## 演示模式
按下Start按钮将启动自动演示模式，按以下顺序展示所有LED模式：
1. 常亮模式 (3秒)
2. 红色慢闪 (4秒)
3. 红色快闪 (4秒)
4. 绿色慢闪 (4秒)
5. 绿色快闪 (4秒)
6. 蓝色慢闪 (4秒)
7. 蓝色快闪 (4秒)
8. 黄色慢闪 (4秒)
9. 黄色快闪 (4秒)
10. 紫色慢闪 (4秒)
11. 紫色快闪 (4秒)
12. 返回常亮 (3秒)

## 技术特性
- **防抖动**: 所有按钮都有0.3秒防抖时间，Start按钮有1秒防抖时间
- **非阻塞演示**: 演示模式在独立线程中运行，不影响车辆控制
- **实时反馈**: 每次LED模式切换都会在终端显示详细信息
- **兼容性**: 完全兼容现有的LED控制系统

## 故障排除
1. **手柄无响应**: 检查`/dev/input/js0`设备是否存在
2. **LED无变化**: 确认LED控制节点正在运行
3. **按钮延迟**: 正常现象，防抖机制保护

## 与interactive_led_control.py的对比
| 功能 | interactive_led_control.py | Xbox手柄控制器 |
|------|---------------------------|----------------|
| LED模式数量 | 11种 (0-10) | 11种 (0-10) ✓ |
| 演示模式 | 支持 | 支持 ✓ |
| 实时切换 | 键盘输入 | 手柄按钮 ✓ |
| 模式循环 | 手动输入 | 自动循环 ✓ |
| 快速访问 | 无 | LB/RB快捷键 ✓ |
| 车辆控制 | 无 | 全向运动 ✓ |

## 开发者信息
- 基于原有`xbox_controller_local.cpp`扩展
- 新增功能完全向后兼容
- 支持多线程安全的演示模式
- 遵循ROS最佳实践
