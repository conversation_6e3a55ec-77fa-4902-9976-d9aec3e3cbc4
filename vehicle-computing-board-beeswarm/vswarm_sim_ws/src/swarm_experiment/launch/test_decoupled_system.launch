<?xml version="1.0"?>
<launch>
    <!-- 测试解耦群体机器人系统启动文件 -->
    
    <!-- 参数配置 -->
    <rosparam file="$(find swarm_experiment)/config/swarm_config.yaml" command="load" />
    
    <!-- 只启动核心节点进行测试 -->
    
    <!-- 灯语分析节点 -->
    <node name="flash_analysis_node" pkg="swarm_experiment" type="flash_analysis_node.py" output="screen">
        <param name="flash_num_frames" value="36" />
        <param name="flash_threshold" value="0.2" />
        <param name="publish_rate" value="5.0" />
    </node>
    
    <!-- 空间分析节点 -->
    <node name="spatial_analysis_node" pkg="swarm_experiment" type="spatial_analysis_node.py" output="screen">
        <param name="real_width" value="0.31" />
        <param name="max_distance" value="10.0" />
        <param name="publish_rate" value="10.0" />
    </node>
    
    <!-- 运动预测节点 -->
    <node name="motion_predictor_node" pkg="swarm_experiment" type="motion_predictor_node.py" output="screen">
        <param name="prediction_horizon" value="2.0" />
        <param name="publish_rate" value="20.0" />
    </node>
    
    <!-- 群体行为决策节点 -->
    <node name="swarm_behavior_node" pkg="swarm_experiment" type="swarm_behavior_node.py" output="screen">
        <param name="decision_rate" value="5.0" />
        <param name="min_confidence" value="0.3" />
    </node>
    
    <!-- 实时反馈控制器 -->
    <node name="feedback_controller_node" pkg="swarm_experiment" type="feedback_controller_node.py" output="screen">
        <param name="control_rate" value="50.0" />
        <param name="max_correction" value="0.2" />
    </node>
    
</launch>
