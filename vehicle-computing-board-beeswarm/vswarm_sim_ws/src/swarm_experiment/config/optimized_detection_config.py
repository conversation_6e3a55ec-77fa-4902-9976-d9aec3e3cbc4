#!/usr/bin/env python3
"""
优化版本的检测配置
方案2：提高Follower响应速度的参数优化
"""

# 优化后的检测参数
OPTIMIZED_DETECTION_CONFIG = {
    # 检测时间优化
    'detection_duration': 3,                    # 5→3秒，减少检测时间
    'min_detection_duration': 0.5,              # 1→0.5秒，更快早停
    'early_stop_no_target_duration': 1.0,       # 2→1秒，更快早停
    'early_stop_check_interval': 0.5,           # 1→0.5秒，更频繁检查
    
    # 灯语分析优化
    'flash_num_frames': 24,                     # 36→24帧，减少分析窗口
    'flash_start_frame': 5,                     # 7→5帧，更早开始分析
    'flash_threshold': 0.25,                    # 0.2→0.25，稍微放宽容忍度
    
    # 其他保持不变的参数
    'cameras': ['/cam0/image_raw', '/cam1/image_raw', '/cam2/image_raw', '/cam3/image_raw'],
    'base_path': '/home/<USER>/nnDataset/0920/labels',
    'real_width': 0.31,
    'early_stop_enabled': True,
}

def apply_optimized_config(config_instance):
    """
    应用优化配置到现有的SwarmConfig实例
    
    Args:
        config_instance: SwarmConfig实例
    """
    for key, value in OPTIMIZED_DETECTION_CONFIG.items():
        if key in config_instance.config:
            old_value = config_instance.config[key]
            config_instance.config[key] = value
            print(f"🔧 优化参数 {key}: {old_value} → {value}")
        else:
            config_instance.config[key] = value
            print(f"🆕 新增参数 {key}: {value}")
    
    print("✅ 检测参数优化完成")
    print(f"📊 预期响应时间: 4-7秒 (原来8-13秒)")

if __name__ == '__main__':
    # 测试配置
    print("🔧 优化版检测配置参数:")
    for key, value in OPTIMIZED_DETECTION_CONFIG.items():
        print(f"  {key}: {value}")
