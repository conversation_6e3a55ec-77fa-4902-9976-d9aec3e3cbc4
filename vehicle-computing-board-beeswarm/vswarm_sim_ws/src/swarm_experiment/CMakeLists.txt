cmake_minimum_required(VERSION 3.0.2)
project(swarm_experiment)

## Find catkin macros and libraries
find_package(catkin REQUIRED COMPONENTS
  rospy
  std_msgs
  std_srvs
  geometry_msgs
  sensor_msgs
  gazebo_msgs
  message_generation
)

## Generate messages in the 'msg' folder
add_message_files(
  FILES
  DetectionResult.msg
  DetectionArray.msg
  FlashPattern.msg
  FlashPatternArray.msg
  SpatialInfo.msg
  SpatialInfoArray.msg
  SwarmCommand.msg
  PredictedState.msg
  PredictedStateArray.msg
  ExecutionFeedback.msg
)

## Generate services in the 'srv' folder
add_service_files(
  FILES
  SetLight.srv
)

## Generate added messages and services with any dependencies listed here
generate_messages(
  DEPENDENCIES
  std_msgs
)

## The catkin_package macro generates cmake config files for your package
catkin_package(
  CATKIN_DEPENDS rospy std_msgs std_srvs geometry_msgs sensor_msgs gazebo_msgs message_runtime
)

## Mark executable scripts (Python etc.) for installation
catkin_install_python(PROGRAMS
  scripts/leader_behavior_node.py
  scripts/led_service_node.py
  scripts/csv_logger_node.py
  scripts/experiment_monitor.py
  scripts/follower_wrapper.py
  nodes/vision_detection_node.py
  nodes/flash_analysis_node.py
  nodes/spatial_analysis_node.py
  nodes/swarm_behavior_node.py
  nodes/motion_predictor_node.py
  nodes/feedback_controller_node.py
  scripts/system_monitor.py
  DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
)
