cmake_minimum_required(VERSION 3.0.2)
project(swarm_experiment)

## 查找catkin宏和库
find_package(catkin REQUIRED COMPONENTS
  rospy
  std_msgs
  geometry_msgs
  sensor_msgs
  message_generation
)

## 添加消息文件
add_message_files(
  FILES
  DetectionResult.msg
  DetectionArray.msg
  FlashPattern.msg
  FlashPatternArray.msg
  SpatialInfo.msg
  SpatialInfoArray.msg
  SwarmCommand.msg
  PredictedState.msg
  PredictedStateArray.msg
  ExecutionFeedback.msg
)

## 生成消息
generate_messages(
  DEPENDENCIES
  std_msgs
  geometry_msgs
)

## catkin包配置
catkin_package(
  CATKIN_DEPENDS 
    rospy 
    std_msgs 
    geometry_msgs 
    sensor_msgs 
    message_runtime
)

## 包含目录
include_directories(
  ${catkin_INCLUDE_DIRS}
)

## 安装Python脚本
catkin_install_python(PROGRAMS
  nodes/vision_detection_node.py
  nodes/flash_analysis_node.py
  nodes/spatial_analysis_node.py
  nodes/swarm_behavior_node.py
  nodes/motion_predictor_node.py
  nodes/feedback_controller_node.py
  tools/system_monitor.py
  DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
)
