# 解耦群体机器人系统使用指南

## 🎯 系统概述

本系统将原有的紧耦合群体机器人系统重构为解耦的分布式架构，通过ROS话题实现异步通信，消除了单进程阻塞问题。

## 🏗️ 系统架构

### 核心组件

1. **视觉检测节点** (`vision_detection_node.py`)
   - 负责多摄像头目标检测
   - 异步发布检测结果到 `/detection_results` 话题
   - 支持智能早停和实时检测

2. **灯语分析节点** (`flash_analysis_node.py`)
   - 订阅检测结果，分析闪烁模式
   - 发布灯语指令到 `/flash_patterns` 话题
   - 支持多目标并行分析

3. **空间分析节点** (`spatial_analysis_node.py`)
   - 订阅检测结果，计算空间位置信息
   - 发布空间数据到 `/spatial_info` 话题
   - 实时计算距离、方位角等信息

4. **群体行为决策节点** (`swarm_behavior_node.py`)
   - 融合灯语指令和空间信息
   - 做出群体行为决策
   - 发布运动控制指令到 `/vel_cmd` 话题

5. **系统监控工具** (`system_monitor.py`)
   - 实时监控系统运行状态
   - 提供性能统计和健康检查

## 🚀 快速启动

### 1. 编译消息类型
```bash
cd /home/<USER>/BeeSwarm/Code/ros/V1
catkin_make
source devel/setup.bash
```

### 2. 启动完整系统
```bash
roslaunch swarm_experiment decoupled_swarm_system.launch
```

### 3. 启动系统监控
```bash
rosrun swarm_experiment system_monitor.py
```

## 📊 话题接口

### 发布话题
- `/detection_results` (DetectionArray): 视觉检测结果
- `/flash_patterns` (FlashPatternArray): 灯语模式识别结果
- `/spatial_info` (SpatialInfoArray): 空间位置信息
- `/swarm_commands` (SwarmCommand): 群体行为指令
- `/vel_cmd` (Twist): 运动控制指令

### 订阅话题
- `/cam0/image_raw`, `/cam1/image_raw`, `/cam2/image_raw`, `/cam3/image_raw`: 摄像头图像

## ⚙️ 配置参数

主要配置文件: `config/swarm_config.yaml`

### 关键参数
- `vision_detection.publish_rate`: 检测结果发布频率
- `flash_analysis.publish_rate`: 灯语分析频率
- `spatial_analysis.publish_rate`: 空间分析频率
- `swarm_behavior.decision_rate`: 行为决策频率

## 🔧 性能优化

### 1. 异步处理优势
- **并行检测**: 多摄像头同时检测，不相互阻塞
- **流水线处理**: 检测、分析、决策并行进行
- **实时响应**: 各组件独立运行，响应更快

### 2. 资源管理
- **内存优化**: 使用滑动窗口缓存，自动清理过期数据
- **CPU负载均衡**: 各节点分布在不同进程，充分利用多核
- **网络优化**: ROS话题异步通信，减少等待时间

## 🐛 调试和监控

### 1. 查看话题状态
```bash
# 查看所有话题
rostopic list

# 监控检测结果
rostopic echo /detection_results

# 监控灯语模式
rostopic echo /flash_patterns

# 监控空间信息
rostopic echo /spatial_info

# 监控群体指令
rostopic echo /swarm_commands
```

### 2. 性能监控
```bash
# 查看话题频率
rostopic hz /detection_results
rostopic hz /flash_patterns
rostopic hz /spatial_info

# 查看节点状态
rosnode list
rosnode info vision_detection_node
```

### 3. 系统监控工具
运行 `system_monitor.py` 可以实时查看:
- 各组件处理速率
- 数据流量统计
- 系统健康状态
- 最近活动记录

## 🔄 与原系统对比

### 原系统问题
- ✗ 串行处理，单点阻塞
- ✗ 紧耦合，难以扩展
- ✗ 单进程，资源竞争
- ✗ 同步等待，响应延迟

### 新系统优势
- ✅ 并行处理，无阻塞
- ✅ 松耦合，易于扩展
- ✅ 多进程，资源隔离
- ✅ 异步通信，实时响应

## 📈 性能提升

预期性能改进:
- **检测延迟**: 减少 60-80%
- **系统吞吐量**: 提升 3-5倍
- **资源利用率**: 提升 40-60%
- **扩展性**: 支持更多摄像头和目标

## 🛠️ 扩展开发

### 添加新的分析节点
1. 创建新的节点文件
2. 定义输入/输出消息类型
3. 在launch文件中添加节点
4. 更新配置文件

### 自定义行为策略
1. 修改 `swarm_behavior_node.py`
2. 添加新的行为函数
3. 更新指令映射
4. 调整优先级配置

## 🔒 注意事项

1. **消息同步**: 确保各节点时钟同步
2. **网络延迟**: 监控ROS话题延迟
3. **资源限制**: 根据硬件调整发布频率
4. **错误处理**: 各节点独立错误恢复

## 📞 技术支持

如遇问题，请检查:
1. ROS环境是否正确配置
2. 消息类型是否正确编译
3. 节点是否正常启动
4. 话题连接是否正常

使用系统监控工具可以快速诊断问题所在。
