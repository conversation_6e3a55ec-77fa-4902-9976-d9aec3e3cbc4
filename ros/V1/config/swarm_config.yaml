# 群体机器人系统配置文件

# 摄像头配置
cameras:
  - "/cam0/image_raw"
  - "/cam1/image_raw" 
  - "/cam2/image_raw"
  - "/cam3/image_raw"

# 摄像头朝向配置 (度)
camera_orientations:
  cam0: 270  # -X轴方向
  cam1: 180  # -Y轴方向
  cam2: 90   # +X轴方向
  cam3: 0    # +Y轴方向

# 视觉检测配置
vision_detection:
  model_path: "/home/<USER>/1206Cars-11/weights/best.engine"
  detection_duration: 5.0
  early_stop_enabled: true
  early_stop_no_target_duration: 2.0
  min_detection_duration: 1.0
  publish_rate: 10.0

# 灯语分析配置
flash_analysis:
  flash_num_frames: 36
  flash_start_frame: 7
  flash_threshold: 0.2
  analysis_window: 5.0
  min_detections: 10
  publish_rate: 5.0

# 空间分析配置
spatial_analysis:
  real_width: 0.31
  analysis_window: 3.0
  min_detections: 5
  publish_rate: 10.0
  max_distance: 10.0

# 群体行为配置
swarm_behavior:
  decision_rate: 5.0
  data_timeout: 2.0
  min_confidence: 0.3
  spatial_weight: 0.6
  flash_weight: 0.4

# 运动参数
spatial_params:
  target_distance: 2.0
  safe_distance: 1.5
  follow_distance: 2.5
  max_distance: 10.0
  angle_tolerance: 30.0
  approach_speed: 0.5
  retreat_speed: 0.4
  turn_speed: 0.4

# 指令优先级
command_priority:
  avoid: 1
  retreat: 2
  approach: 3
  follow: 4
  circle: 5
  align: 6
  parallel: 7
  stop: 8

# 群体指令映射
swarm_commands:
  1: "approach"
  2: "follow"
  3: "avoid"
  4: "circle"
  5: "stop"
  6: "align"
  7: "retreat"
  8: "parallel"
