<?xml version="1.0"?>
<launch>
    <!-- 解耦群体机器人系统启动文件 -->
    
    <!-- 参数配置 -->
    <rosparam file="$(find swarm_experiment)/config/swarm_config.yaml" command="load" />
    
    <!-- 视觉检测节点 -->
    <node name="vision_detection_node" pkg="swarm_experiment" type="vision_detection_node.py" output="screen">
        <param name="model_path" value="/home/<USER>/1206Cars-11/weights/best.engine" />
        <param name="detection_duration" value="5.0" />
        <param name="publish_rate" value="10.0" />
    </node>
    
    <!-- 灯语分析节点 -->
    <node name="flash_analysis_node" pkg="swarm_experiment" type="flash_analysis_node.py" output="screen">
        <param name="flash_num_frames" value="36" />
        <param name="flash_threshold" value="0.2" />
        <param name="publish_rate" value="5.0" />
    </node>
    
    <!-- 空间分析节点 -->
    <node name="spatial_analysis_node" pkg="swarm_experiment" type="spatial_analysis_node.py" output="screen">
        <param name="real_width" value="0.31" />
        <param name="max_distance" value="10.0" />
        <param name="publish_rate" value="10.0" />
    </node>
    
    <!-- 群体行为决策节点 -->
    <node name="swarm_behavior_node" pkg="swarm_experiment" type="swarm_behavior_node.py" output="screen">
        <param name="decision_rate" value="5.0" />
        <param name="min_confidence" value="0.3" />
    </node>
    
    <!-- RViz可视化 (可选) -->
    <node name="rviz" pkg="rviz" type="rviz" args="-d $(find swarm_experiment)/config/swarm_visualization.rviz" if="$(arg use_rviz)" />
    
    <!-- 参数 -->
    <arg name="use_rviz" default="false" />
    
</launch>
