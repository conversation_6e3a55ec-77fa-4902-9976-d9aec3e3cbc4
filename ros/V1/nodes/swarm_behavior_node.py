#!/usr/bin/env python3
"""
群体行为决策节点 - 解耦版本
负责融合灯语指令和空间信息，做出群体行为决策
"""

import rospy
import threading
import time
import math
from collections import defaultdict
from std_msgs.msg import Header
from geometry_msgs.msg import Twist
from swarm_msgs.msg import FlashPatternArray, SpatialInfoArray, SwarmCommand, PredictedStateArray, ExecutionFeedback

class SwarmBehaviorNode:
    """群体行为决策节点"""
    
    def __init__(self):
        rospy.init_node('swarm_behavior_node', anonymous=True)
        
        # 配置参数
        self.config = {
            'decision_rate': 5.0,        # 决策频率(Hz)
            'data_timeout': 2.0,         # 数据超时时间(秒)
            'min_confidence': 0.3,       # 最小置信度阈值
            'spatial_weight': 0.6,       # 空间信息权重
            'flash_weight': 0.4          # 灯语信息权重
        }
        
        # 群体行为参数
        self.spatial_params = {
            'target_distance': 2.0,
            'safe_distance': 1.5,
            'follow_distance': 2.5,
            'max_distance': 10.0,
            'angle_tolerance': 30.0,
            'approach_speed': 0.5,
            'retreat_speed': 0.4,
            'turn_speed': 0.4
        }
        
        # 指令优先级
        self.COMMAND_PRIORITY = {
            "avoid": 1,
            "retreat": 2,
            "approach": 3,
            "follow": 4,
            "circle": 5,
            "align": 6,
            "parallel": 7,
            "stop": 8
        }
        
        # 摄像头朝向配置
        self.camera_orientations = {
            'cam0': 270, 'cam1': 180, 'cam2': 90, 'cam3': 0
        }
        
        # 数据缓存
        self.flash_data = {}
        self.spatial_data = {}
        self.predicted_data = {}
        self.execution_feedback = {}
        self.data_lock = threading.Lock()
        
        # 发布器和订阅器
        self.command_pub = rospy.Publisher('/swarm_commands', SwarmCommand, queue_size=10)
        self.vel_pub = rospy.Publisher('/vel_cmd', Twist, queue_size=10)
        
        self.flash_sub = rospy.Subscriber('/flash_patterns', FlashPatternArray, self._flash_callback)
        self.spatial_sub = rospy.Subscriber('/spatial_info', SpatialInfoArray, self._spatial_callback)
        self.prediction_sub = rospy.Subscriber('/predicted_states', PredictedStateArray, self._prediction_callback)
        self.feedback_sub = rospy.Subscriber('/execution_feedback', ExecutionFeedback, self._feedback_callback)
        
        # 启动决策线程
        self.decision_active = True
        self.decision_thread = threading.Thread(target=self._decision_loop)
        self.decision_thread.daemon = True
        self.decision_thread.start()
        
        rospy.loginfo("🧠 群体行为决策节点初始化完成")
    
    def _flash_callback(self, msg):
        """灯语模式回调"""
        current_time = time.time()
        
        with self.data_lock:
            for pattern in msg.patterns:
                key = (pattern.camera_name, pattern.track_id)
                self.flash_data[key] = {
                    'command_name': pattern.command_name,
                    'command_id': pattern.command_id,
                    'pattern': pattern.pattern,
                    'confidence': pattern.confidence,
                    'timestamp': current_time,
                    'priority': self.COMMAND_PRIORITY.get(pattern.command_name, 99)
                }
    
    def _spatial_callback(self, msg):
        """空间信息回调"""
        current_time = time.time()
        
        with self.data_lock:
            for spatial in msg.spatial_infos:
                key = (spatial.camera_name, spatial.track_id)
                self.spatial_data[key] = {
                    'distance': spatial.distance,
                    'azimuth': spatial.azimuth,
                    'world_azimuth': spatial.world_azimuth,
                    'position_x': spatial.position_x,
                    'position_y': spatial.position_y,
                    'confidence': spatial.confidence,
                    'detection_count': spatial.detection_count,
                    'timestamp': current_time
                }

    def _prediction_callback(self, msg):
        """预测状态回调"""
        current_time = time.time()

        with self.data_lock:
            for pred in msg.predictions:
                if pred.entity_type == "target":
                    key = ("target", int(pred.entity_id))
                    self.predicted_data[key] = {
                        'position_x': pred.position_x,
                        'position_y': pred.position_y,
                        'velocity_x': pred.velocity_x,
                        'velocity_y': pred.velocity_y,
                        'uncertainty': pred.uncertainty_radius,
                        'confidence': pred.confidence,
                        'predicted_time': pred.predicted_time,
                        'timestamp': current_time
                    }

    def _feedback_callback(self, msg):
        """执行反馈回调"""
        current_time = time.time()

        with self.data_lock:
            key = msg.track_id
            self.execution_feedback[key] = {
                'command_name': msg.command_name,
                'position_error': math.sqrt(msg.position_error_x**2 + msg.position_error_y**2),
                'heading_error': abs(msg.heading_error),
                'is_converged': msg.is_converged,
                'timestamp': current_time
            }
    
    def _decision_loop(self):
        """决策循环线程"""
        rate = rospy.Rate(self.config['decision_rate'])
        
        while self.decision_active and not rospy.is_shutdown():
            try:
                self._make_decision()
            except Exception as e:
                rospy.logwarn(f"决策错误: {e}")
            
            rate.sleep()
    
    def _make_decision(self):
        """做出群体行为决策"""
        current_time = time.time()
        candidates = []
        
        with self.data_lock:
            # 清理过期数据
            self._cleanup_expired_data(current_time)
            
            # 寻找有效的候选目标
            for key in set(self.flash_data.keys()) & set(self.spatial_data.keys()):
                flash_info = self.flash_data[key]
                spatial_info = self.spatial_data[key]
                
                # 检查数据时效性和置信度
                if (current_time - flash_info['timestamp'] < self.config['data_timeout'] and
                    current_time - spatial_info['timestamp'] < self.config['data_timeout'] and
                    flash_info['confidence'] >= self.config['min_confidence'] and
                    spatial_info['confidence'] >= self.config['min_confidence']):
                    
                    # 计算综合评分
                    score = self._calculate_target_score(flash_info, spatial_info)
                    
                    candidate = {
                        'key': key,
                        'flash_info': flash_info,
                        'spatial_info': spatial_info,
                        'score': score
                    }
                    candidates.append(candidate)
        
        if not candidates:
            return
        
        # 选择最优候选目标
        best_candidate = max(candidates, key=lambda x: x['score'])
        
        # 执行行为决策
        self._execute_behavior(best_candidate)
    
    def _calculate_target_score(self, flash_info, spatial_info):
        """计算目标综合评分 - 增强版本，考虑预测和反馈"""
        # 优先级评分（优先级越高分数越高）
        priority_score = (10 - flash_info['priority']) / 10.0

        # 使用预测位置计算距离评分
        distance = spatial_info['distance']

        # 检查是否有预测数据
        key = ('target', spatial_info.get('track_id', -1))
        if key in self.predicted_data:
            pred_data = self.predicted_data[key]
            # 使用预测位置重新计算距离
            pred_distance = math.sqrt(pred_data['position_x']**2 + pred_data['position_y']**2)
            # 加权平均当前距离和预测距离
            distance = 0.6 * distance + 0.4 * pred_distance

            # 考虑预测不确定性
            uncertainty_penalty = min(pred_data['uncertainty'] / 2.0, 0.3)
        else:
            uncertainty_penalty = 0.0

        # 距离评分
        if distance < self.spatial_params['safe_distance']:
            distance_score = 0.3  # 太近
        elif distance > self.spatial_params['max_distance']:
            distance_score = 0.1  # 太远
        else:
            ideal_distance = self.spatial_params['target_distance']
            distance_score = 1.0 - abs(distance - ideal_distance) / ideal_distance

        # 置信度评分
        confidence_score = (flash_info['confidence'] + spatial_info['confidence']) / 2.0

        # 执行反馈评分
        feedback_score = 1.0
        track_id = spatial_info.get('track_id', -1)
        if track_id in self.execution_feedback:
            feedback = self.execution_feedback[track_id]
            if feedback['is_converged']:
                feedback_score = 1.2  # 奖励收敛的目标
            elif feedback['position_error'] > 1.0:
                feedback_score = 0.8  # 惩罚误差大的目标

        # 综合评分
        total_score = (priority_score * 0.4 +
                      distance_score * 0.3 +
                      confidence_score * 0.2 +
                      feedback_score * 0.1) - uncertainty_penalty

        return max(0.0, total_score)
    
    def _execute_behavior(self, candidate):
        """执行群体行为"""
        flash_info = candidate['flash_info']
        spatial_info = candidate['spatial_info']
        cam_name, track_id = candidate['key']
        
        command_name = flash_info['command_name']
        distance = spatial_info['distance']
        world_azimuth = spatial_info['world_azimuth']
        
        rospy.loginfo(f"🚀 执行行为: {command_name} | TrackID{track_id} | 距离{distance:.2f}m | 方位{world_azimuth:.1f}°")
        
        # 发布群体指令消息
        command_msg = SwarmCommand()
        command_msg.header = Header()
        command_msg.header.stamp = rospy.Time.now()
        command_msg.command_name = command_name
        command_msg.command_id = flash_info['command_id']
        command_msg.target_distance = distance
        command_msg.target_azimuth = world_azimuth
        command_msg.camera_name = cam_name
        command_msg.track_id = track_id
        
        self.command_pub.publish(command_msg)
        
        # 计算并发布运动指令
        twist = self._calculate_motion_command(command_name, distance, world_azimuth)
        if twist:
            self.vel_pub.publish(twist)
            rospy.loginfo(f"📤 发布运动指令: linear.x={twist.linear.x:.3f}, linear.y={twist.linear.y:.3f}")
    
    def _calculate_motion_command(self, command_name, distance, world_azimuth):
        """计算运动指令"""
        twist = Twist()
        
        if command_name == "approach":
            self._approach_behavior(twist, distance, world_azimuth)
        elif command_name == "follow":
            self._follow_behavior(twist, distance, world_azimuth)
        elif command_name == "avoid":
            self._avoid_behavior(twist, distance, world_azimuth)
        elif command_name == "circle":
            self._circle_behavior(twist, distance, world_azimuth)
        elif command_name == "retreat":
            self._retreat_behavior(twist, distance, world_azimuth)
        elif command_name == "parallel":
            self._parallel_behavior(twist, distance, world_azimuth)
        elif command_name == "stop":
            # 停止指令，所有速度为0
            pass
        else:
            return None
        
        return twist
    
    def _approach_behavior(self, twist, distance, world_azimuth):
        """靠近行为"""
        if distance <= self.spatial_params['target_distance']:
            return
        
        angle_rad = math.radians(world_azimuth)
        speed = self.spatial_params['approach_speed']
        
        if distance < self.spatial_params['safe_distance']:
            speed *= 0.5
        
        twist.linear.x = speed * math.sin(angle_rad)
        twist.linear.y = speed * math.cos(angle_rad)
    
    def _follow_behavior(self, twist, distance, world_azimuth):
        """跟随行为"""
        target_distance = self.spatial_params['follow_distance']
        distance_error = distance - target_distance
        
        if abs(distance_error) > 0.5:
            angle_rad = math.radians(world_azimuth)
            speed = self.spatial_params['approach_speed'] * 0.7
            
            if distance_error < 0:
                speed = -self.spatial_params['retreat_speed'] * 0.5
            
            twist.linear.x = speed * math.sin(angle_rad)
            twist.linear.y = speed * math.cos(angle_rad)
    
    def _avoid_behavior(self, twist, distance, world_azimuth):
        """避让行为"""
        avoid_angle = (world_azimuth + 180) % 360
        angle_rad = math.radians(avoid_angle)
        speed = self.spatial_params['retreat_speed']
        
        twist.linear.x = speed * math.sin(angle_rad)
        twist.linear.y = speed * math.cos(angle_rad)
    
    def _circle_behavior(self, twist, distance, world_azimuth):
        """环绕行为"""
        circle_angle = (world_azimuth + 90) % 360
        angle_rad = math.radians(circle_angle)
        speed = self.spatial_params['approach_speed'] * 0.7
        
        twist.linear.x = speed * math.sin(angle_rad)
        twist.linear.y = speed * math.cos(angle_rad)
    
    def _retreat_behavior(self, twist, distance, world_azimuth):
        """后退行为"""
        retreat_angle = (world_azimuth + 180) % 360
        angle_rad = math.radians(retreat_angle)
        speed = self.spatial_params['retreat_speed']
        
        twist.linear.x = speed * math.sin(angle_rad)
        twist.linear.y = speed * math.cos(angle_rad)
    
    def _parallel_behavior(self, twist, distance, world_azimuth):
        """平行行为"""
        parallel_angle = (world_azimuth + 90) % 360
        angle_rad = math.radians(parallel_angle)
        speed = self.spatial_params['approach_speed']
        
        twist.linear.x = speed * math.sin(angle_rad)
        twist.linear.y = speed * math.cos(angle_rad)
    
    def _cleanup_expired_data(self, current_time):
        """清理过期数据"""
        timeout = self.config['data_timeout']
        
        expired_flash_keys = [k for k, v in self.flash_data.items() 
                             if current_time - v['timestamp'] > timeout]
        expired_spatial_keys = [k for k, v in self.spatial_data.items() 
                               if current_time - v['timestamp'] > timeout]
        
        for key in expired_flash_keys:
            del self.flash_data[key]
        for key in expired_spatial_keys:
            del self.spatial_data[key]
    
    def shutdown(self):
        """关闭节点"""
        self.decision_active = False
        rospy.loginfo("🛑 群体行为决策节点关闭")

def main():
    try:
        node = SwarmBehaviorNode()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
    finally:
        if 'node' in locals():
            node.shutdown()

if __name__ == '__main__':
    main()
