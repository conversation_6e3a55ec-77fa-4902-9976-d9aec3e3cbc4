#!/usr/bin/env python3
"""
实时反馈控制器 - 监控执行效果并动态调整行为
解决决策过时和执行偏差问题
"""

import rospy
import math
import time
from collections import deque
from std_msgs.msg import Header
from geometry_msgs.msg import Twist
from swarm_msgs.msg import SwarmCommand, PredictedStateArray, ExecutionFeedback

class FeedbackControllerNode:
    """实时反馈控制器"""
    
    def __init__(self):
        rospy.init_node('feedback_controller_node', anonymous=True)
        
        # 配置参数
        self.config = {
            'control_rate': 50.0,         # 控制频率(Hz)
            'prediction_weight': 0.7,     # 预测权重
            'feedback_weight': 0.3,       # 反馈权重
            'max_correction': 0.2,        # 最大修正幅度
            'convergence_threshold': 0.1,  # 收敛阈值
            'timeout_threshold': 5.0      # 指令超时阈值
        }
        
        # 当前状态
        self.current_command = None
        self.command_start_time = None
        self.execution_history = deque(maxlen=100)
        self.predicted_states = {}
        
        # 控制参数
        self.pid_params = {
            'kp': 1.0,    # 比例增益
            'ki': 0.1,    # 积分增益  
            'kd': 0.05,   # 微分增益
        }
        
        # PID状态
        self.error_integral = {'x': 0.0, 'y': 0.0, 'theta': 0.0}
        self.last_error = {'x': 0.0, 'y': 0.0, 'theta': 0.0}
        self.last_time = time.time()
        
        # ROS接口
        self.corrected_vel_pub = rospy.Publisher('/corrected_vel_cmd', Twist, queue_size=10)
        self.feedback_pub = rospy.Publisher('/execution_feedback', ExecutionFeedback, queue_size=10)
        
        self.command_sub = rospy.Subscriber('/swarm_commands', SwarmCommand, self._command_callback)
        self.prediction_sub = rospy.Subscriber('/predicted_states', PredictedStateArray, self._prediction_callback)
        self.vel_sub = rospy.Subscriber('/vel_cmd', Twist, self._velocity_callback)
        
        # 启动控制循环
        import threading
        self.control_active = True
        self.control_thread = threading.Thread(target=self._control_loop)
        self.control_thread.daemon = True
        self.control_thread.start()
        
        rospy.loginfo("🎮 实时反馈控制器初始化完成")
    
    def _command_callback(self, msg):
        """群体指令回调"""
        self.current_command = msg
        self.command_start_time = time.time()
        
        # 重置PID状态
        self.error_integral = {'x': 0.0, 'y': 0.0, 'theta': 0.0}
        self.last_error = {'x': 0.0, 'y': 0.0, 'theta': 0.0}
        
        rospy.loginfo(f"🎯 接收新指令: {msg.command_name}, 目标距离: {msg.target_distance:.2f}m")
    
    def _prediction_callback(self, msg):
        """预测状态回调"""
        self.predicted_states = {}
        
        for pred in msg.predictions:
            key = (pred.entity_type, pred.entity_id)
            if key not in self.predicted_states:
                self.predicted_states[key] = []
            self.predicted_states[key].append(pred)
    
    def _velocity_callback(self, msg):
        """原始速度指令回调"""
        if self.current_command:
            # 记录执行历史
            execution_record = {
                'timestamp': time.time(),
                'command': self.current_command.command_name,
                'original_vel': (msg.linear.x, msg.linear.y, msg.angular.z),
                'target_distance': self.current_command.target_distance,
                'target_azimuth': self.current_command.target_azimuth
            }
            self.execution_history.append(execution_record)
    
    def _control_loop(self):
        """实时控制循环"""
        rate = rospy.Rate(self.config['control_rate'])
        
        while self.control_active and not rospy.is_shutdown():
            try:
                if self.current_command:
                    self._update_control()
            except Exception as e:
                rospy.logwarn(f"控制循环错误: {e}")
            
            rate.sleep()
    
    def _update_control(self):
        """更新控制指令"""
        current_time = time.time()
        
        # 检查指令是否超时
        if current_time - self.command_start_time > self.config['timeout_threshold']:
            rospy.logwarn("指令执行超时，停止控制")
            self.current_command = None
            return
        
        # 获取预测的目标状态
        target_prediction = self._get_target_prediction()
        self_prediction = self._get_self_prediction()
        
        if not target_prediction or not self_prediction:
            return
        
        # 计算期望状态
        desired_state = self._calculate_desired_state(target_prediction)
        
        # 计算控制误差
        error = self._calculate_error(desired_state, self_prediction)
        
        # PID控制
        control_output = self._pid_control(error, current_time)
        
        # 生成修正后的速度指令
        corrected_vel = self._generate_corrected_velocity(control_output)
        
        # 发布修正指令
        self.corrected_vel_pub.publish(corrected_vel)
        
        # 发布执行反馈
        self._publish_feedback(error, control_output)
    
    def _get_target_prediction(self):
        """获取目标预测状态"""
        if not self.current_command:
            return None
        
        target_key = ("target", str(self.current_command.track_id))
        
        if target_key in self.predicted_states:
            # 选择最近的预测点（约0.1-0.2秒后）
            predictions = self.predicted_states[target_key]
            current_time = time.time()
            
            best_pred = None
            best_time_diff = float('inf')
            
            for pred in predictions:
                time_diff = abs(pred.predicted_time - (current_time + 0.15))
                if time_diff < best_time_diff:
                    best_time_diff = time_diff
                    best_pred = pred
            
            return best_pred
        
        return None
    
    def _get_self_prediction(self):
        """获取自身预测状态"""
        self_key = ("self", "robot")
        
        if self_key in self.predicted_states:
            predictions = self.predicted_states[self_key]
            current_time = time.time()
            
            # 选择最近的预测点
            best_pred = None
            best_time_diff = float('inf')
            
            for pred in predictions:
                time_diff = abs(pred.predicted_time - (current_time + 0.1))
                if time_diff < best_time_diff:
                    best_time_diff = time_diff
                    best_pred = pred
            
            return best_pred
        
        return None
    
    def _calculate_desired_state(self, target_prediction):
        """计算期望状态"""
        if not target_prediction or not self.current_command:
            return None
        
        command_name = self.current_command.command_name
        target_x = target_prediction.position_x
        target_y = target_prediction.position_y
        
        # 根据不同指令计算期望位置
        if command_name == "approach":
            # 期望距离目标2米
            desired_distance = 2.0
            angle_to_target = math.atan2(target_y, target_x)
            desired_x = target_x - desired_distance * math.cos(angle_to_target)
            desired_y = target_y - desired_distance * math.sin(angle_to_target)
            
        elif command_name == "follow":
            # 期望距离目标2.5米
            desired_distance = 2.5
            angle_to_target = math.atan2(target_y, target_x)
            desired_x = target_x - desired_distance * math.cos(angle_to_target)
            desired_y = target_y - desired_distance * math.sin(angle_to_target)
            
        elif command_name == "avoid":
            # 期望远离目标
            desired_distance = 4.0
            angle_to_target = math.atan2(target_y, target_x)
            desired_x = target_x - desired_distance * math.cos(angle_to_target)
            desired_y = target_y - desired_distance * math.sin(angle_to_target)
            
        elif command_name == "circle":
            # 期望在目标周围环绕
            desired_distance = 3.0
            current_angle = math.atan2(target_y, target_x)
            circle_angle = current_angle + math.pi/2  # 90度偏移
            desired_x = target_x + desired_distance * math.cos(circle_angle)
            desired_y = target_y + desired_distance * math.sin(circle_angle)
            
        else:
            # 默认保持当前位置
            desired_x = target_x
            desired_y = target_y
        
        return {
            'x': desired_x,
            'y': desired_y,
            'theta': math.atan2(target_y, target_x)
        }
    
    def _calculate_error(self, desired_state, self_prediction):
        """计算控制误差"""
        if not desired_state or not self_prediction:
            return {'x': 0.0, 'y': 0.0, 'theta': 0.0}
        
        error_x = desired_state['x'] - self_prediction.position_x
        error_y = desired_state['y'] - self_prediction.position_y
        error_theta = desired_state['theta'] - self_prediction.heading
        
        # 角度误差归一化到[-π, π]
        while error_theta > math.pi:
            error_theta -= 2 * math.pi
        while error_theta < -math.pi:
            error_theta += 2 * math.pi
        
        return {'x': error_x, 'y': error_y, 'theta': error_theta}
    
    def _pid_control(self, error, current_time):
        """PID控制器"""
        dt = current_time - self.last_time
        
        if dt <= 0:
            return {'x': 0.0, 'y': 0.0, 'theta': 0.0}
        
        control_output = {}
        
        for axis in ['x', 'y', 'theta']:
            # 比例项
            p_term = self.pid_params['kp'] * error[axis]
            
            # 积分项
            self.error_integral[axis] += error[axis] * dt
            i_term = self.pid_params['ki'] * self.error_integral[axis]
            
            # 微分项
            d_term = self.pid_params['kd'] * (error[axis] - self.last_error[axis]) / dt
            
            # 总控制输出
            control_output[axis] = p_term + i_term + d_term
            
            # 限制输出幅度
            max_output = self.config['max_correction']
            control_output[axis] = max(-max_output, min(max_output, control_output[axis]))
            
            # 更新历史误差
            self.last_error[axis] = error[axis]
        
        self.last_time = current_time
        return control_output
    
    def _generate_corrected_velocity(self, control_output):
        """生成修正后的速度指令"""
        corrected_vel = Twist()
        
        # 基础速度（来自原始指令）
        base_vx = 0.0
        base_vy = 0.0
        base_omega = 0.0
        
        if self.execution_history:
            last_execution = self.execution_history[-1]
            base_vx, base_vy, base_omega = last_execution['original_vel']
        
        # 添加修正
        corrected_vel.linear.x = base_vx + control_output['x']
        corrected_vel.linear.y = base_vy + control_output['y']
        corrected_vel.angular.z = base_omega + control_output['theta']
        
        # 速度限制
        max_linear = 1.0
        max_angular = 1.0
        
        corrected_vel.linear.x = max(-max_linear, min(max_linear, corrected_vel.linear.x))
        corrected_vel.linear.y = max(-max_linear, min(max_linear, corrected_vel.linear.y))
        corrected_vel.angular.z = max(-max_angular, min(max_angular, corrected_vel.angular.z))
        
        return corrected_vel
    
    def _publish_feedback(self, error, control_output):
        """发布执行反馈"""
        feedback = ExecutionFeedback()
        feedback.header = Header()
        feedback.header.stamp = rospy.Time.now()
        
        if self.current_command:
            feedback.command_name = self.current_command.command_name
            feedback.track_id = self.current_command.track_id
        
        feedback.position_error_x = error['x']
        feedback.position_error_y = error['y']
        feedback.heading_error = error['theta']
        feedback.control_output_x = control_output['x']
        feedback.control_output_y = control_output['y']
        feedback.control_output_theta = control_output['theta']
        
        # 计算总误差
        total_error = math.sqrt(error['x']**2 + error['y']**2)
        feedback.total_position_error = total_error
        feedback.is_converged = total_error < self.config['convergence_threshold']
        
        self.feedback_pub.publish(feedback)
    
    def shutdown(self):
        """关闭节点"""
        self.control_active = False
        rospy.loginfo("🛑 实时反馈控制器关闭")

def main():
    try:
        node = FeedbackControllerNode()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
    finally:
        if 'node' in locals():
            node.shutdown()

if __name__ == '__main__':
    main()
